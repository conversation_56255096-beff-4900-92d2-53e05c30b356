#!/usr/bin/env python3
"""
运行所有测试的脚本
"""

import sys
import os
import importlib
import traceback


from app.llm.LlmFactory import getDefaultLLM

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def run_test_module(module_name):
    """运行单个测试模块"""
    print(f"\n{'='*60}")
    print(f"运行测试模块: {module_name}")
    print(f"{'='*60}")
    
    try:
        # 动态导入测试模块
        module = importlib.import_module(f"app.test.{module_name}")
        
        # 如果模块有main函数，调用它
        if hasattr(module, 'main'):
            module.main()
        else:
            print(f"⚠️ 模块 {module_name} 没有main函数，跳过")
            
        print(f"✅ 测试模块 {module_name} 执行完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试模块 {module_name} 执行失败: {str(e)}")
        print(f"详细错误信息:")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 开始运行所有测试...")
    
    # 定义要运行的测试模块列表
    test_modules = [
        "test_llm_connection",           # LLM连接测试
        "test_checkpointer",             # Checkpointer测试
        "test_intention_agent",          # IntentionAgent基本测试
        "test_intention_agent_advanced", # IntentionAgent高级测试
        "test_enhanced_router_agent",    # 增强版路由Agent测试
        "test_enhanced_router_real_llm", # 增强版路由Agent真实LLM测试
    ]
    
    print(f"计划运行 {len(test_modules)} 个测试模块:")
    for i, module in enumerate(test_modules, 1):
        print(f"  {i}. {module}")
    
    # 运行所有测试
    success_count = 0
    failed_modules = []
    
    for module_name in test_modules:
        try:
            if run_test_module(module_name):
                success_count += 1
            else:
                failed_modules.append(module_name)
        except KeyboardInterrupt:
            print(f"\n⚠️ 用户中断了测试执行")
            break
        except Exception as e:
            print(f"❌ 运行测试模块 {module_name} 时发生意外错误: {str(e)}")
            failed_modules.append(module_name)
    
    # 输出测试结果汇总
    print(f"\n{'='*60}")
    print("测试结果汇总")
    print(f"{'='*60}")
    print(f"总测试模块数: {len(test_modules)}")
    print(f"成功执行: {success_count}")
    print(f"执行失败: {len(failed_modules)}")
    print(f"成功率: {success_count/len(test_modules)*100:.1f}%")
    
    if failed_modules:
        print(f"\n失败的测试模块:")
        for module in failed_modules:
            print(f"  - {module}")
    
    if success_count == len(test_modules):
        print(f"\n🎉 所有测试都成功执行！")
        return True
    else:
        print(f"\n⚠️ 有 {len(failed_modules)} 个测试模块执行失败")
        return False

if __name__ == "__main__":
    import asyncio
    from langchain_core.callbacks import BaseCallbackHandler
    from langchain_core.runnables import RunnableConfig
    from app.llm.LlmFactory import getDefaultLLM

    class CustomCallbackHandler(BaseCallbackHandler):
        def on_llm_start(self, serialized, prompts, **kwargs):
            metadata = kwargs.get("metadata", {})
            print(f"🚀 LLM开始执行，用户: {metadata.get('user', 'unknown')}")

        def on_llm_new_token(self, token, **kwargs):
            # 实时显示生成的每个token
            kwargs.s
            print(token, end='', flush=True)

        def on_llm_end(self, response, **kwargs):
            metadata = kwargs.get("metadata", {})
            print(f"\n✅ LLM执行完成，事件类型: {metadata.get('event_type', 'unknown')}")

    async def run_async_llm():
        llm = getDefaultLLM()

        prompt = """你是一名营销分析专家，请按照以下格式输出内容：
        <thinking>在此写出你的思考过程</thinking>
        <result>在此写出你的最终结论</result>
        问题是：{question}
        """

        question = "这个营销活动是否适合30岁以下人群？"

        config = RunnableConfig(
            metadata={
                "user": "liuyabo",
                "event_type": "thinking_step",
                "context": "marketing_analysis"
            },
            tags=["marketing", "analysis", "testing"],
            run_name="marketing_analysis_run",
            callbacks=[CustomCallbackHandler()]
        )

        print("开始异步流式调用LLM:")
        full_response = ""

        # 使用异步的 astream 方法
        async for chunk in llm.astream(
            prompt.format(question=question),
            config=config
        ):
            if hasattr(chunk, 'content'):
                full_response += chunk.content

        print("\n\n完整响应内容:")
        print(full_response)
        return full_response

    # 运行异步函数
    asyncio.run(run_async_llm())



