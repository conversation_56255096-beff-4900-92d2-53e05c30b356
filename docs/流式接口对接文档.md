# 营销系统流式接口对接文档

## 📋 概述

本文档描述了营销系统流式接口的使用方法，包括接口规范、事件类型、数据结构和前端集成示例。

## 🚀 接口信息

### 基础信息
- **接口地址**: `POST /api/marketing/chat/stream`
- **协议**: HTTP/1.1 + Server-Sent Events (SSE)
- **内容类型**: `text/event-stream`
- **编码**: UTF-8

### 请求格式

#### 请求头
```http
POST /api/marketing/chat/stream HTTP/1.1
Host: your-domain.com
Content-Type: application/json
Accept: text/event-stream
```

#### 请求体
```json
{
  "userInput": "我想做一个暑期促销活动，目标是大学生群体，预算2万元",
  "userId": "user_123",
  "conversationId": "conv_456"
}
```

#### 参数说明
| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| userInput | string | ✅ | 用户输入的营销需求描述 |
| userId | string | ✅ | 用户唯一标识 |
| conversationId | string | ❌ | 对话ID，用于会话管理 |

## 📡 响应格式

### SSE 数据格式
```
data: {"event":"workflowStarted","messageId":"msg_abc123","conversationId":"conv_456","content":"## 🚀 开始执行营销工作流","data":{"totalSteps":5,"workflowType":"marketing"},"createdAt":1753810233047}

data: {"event":"message","messageId":"msg_abc123","conversationId":"conv_456","content":"根据您的需求，我来为您分析...","data":null,"createdAt":1753810233150}
```

### 统一事件结构
所有事件都遵循以下统一结构：

```typescript
interface StreamEvent {
  event: string;           // 事件类型
  messageId: string;       // 消息ID
  conversationId: string;  // 对话ID
  content: string;         // 用户可读的文本内容（支持Markdown格式）
  data: object | null;     // 结构化数据（程序处理用）
  createdAt: number;       // 创建时间戳（毫秒级）
  runId?: string;          // 运行ID（可选，用于关联LLM调用）
}
```

## 🎯 事件类型详解

### 1. workflowStarted - 工作流开始
工作流开始执行时发送，标志着整个营销分析流程的启动。

```json
{
  "event": "workflowStarted",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "## 🚀 开始执行营销工作流",
  "data": {
    "totalSteps": 5,
    "workflowType": "marketing"
  },
  "createdAt": 1753810233047
}
```

### 2. nodeStarted - 节点开始执行
每个Agent节点开始执行时发送，提供节点信息和进度。

```json
{
  "event": "nodeStarted",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "## 🔄 开始执行**意图识别**",
  "data": {
    "nodeId": "INTENTION_AGENT",
    "stepNumber": 2,
    "totalSteps": 5,
    "progressPercent": 40,
    "displayName": "意图识别"
  },
  "createdAt": 1753810233150
}
```

### 3. nodeProgress - 节点执行进度
节点执行过程中发送的进度更新事件。

```json
{
  "event": "nodeProgress",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "正在深入理解您的营销目标...",
  "data": {
    "nodeId": "INTENTION_AGENT",
    "displayName": "意图识别",
    "progress": 30,
    "stage": "intent_analysis_start"
  },
  "createdAt": 1753810233200
}
```

### 4. nodeEnd - 节点执行完成
节点执行完成时发送，包含执行结果和输出数据。

```json
{
  "event": "nodeEnd",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "意图识别执行完成",
  "data": {
    "nodeId": "INTENTION_AGENT",
    "status": "succeeded",
    "stepNumber": 2,
    "elapsedTime": 2.1,
    "outputs": {
      "intentInfo": {
        "goal": "吸引大学生参与促销活动",
        "activityType": "营销活动",
        "audience": "大学生群体",
        "roi": "2万元"
      },
      "needCompletion": false
    },
    "error": null,
    "displayName": "意图识别"
  },
  "createdAt": 1753810235250
}
```

### 5. message - LLM文本流（打字机效果）
LLM生成的文本内容，用于实现打字机效果的流式输出。

```json
{
  "event": "message",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "根据您的需求，我来为您分析",
  "data": null,
  "createdAt": 1753810233300,
  "runId": "run_def456"
}
```

### 6. workflowEnd - 工作流结束
工作流执行完成时发送，包含执行统计信息。

```json
{
  "event": "workflowEnd",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "营销工作流执行完成",
  "data": {
    "status": "succeeded",
    "totalSteps": 5,
    "completedSteps": 5,
    "elapsedTime": 12.5,
    "successRate": 1.0
  },
  "createdAt": 1753810245550
}
```

### 7. interrupt - 工作流中断
当需要用户补充信息时发送的中断事件。

```json
{
  "event": "interrupt",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "工作流已中断，等待用户补充信息",
  "data": {
    "threadId": "thread_789",
    "nextNode": "TAG_CUSTOMER_AGENT",
    "status": "interrupted",
    "statusText": "需要更多信息才能继续",
    "showContinue": true,
    "canResume": true
  },
  "createdAt": 1753810240000
}
```

### 8. messageEnd - 消息结束
标志着一轮消息生成的结束。

```json
{
  "event": "messageEnd",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "回复生成完成",
  "data": {
    "totalEvents": 25,
    "executionTime": 12.5,
    "metadata": {
      "workflowInfo": {
        "totalEvents": 25,
        "executionTime": 12.5,
        "successRate": 1.0
      }
    }
  },
  "createdAt": 1753810245600
}
```

### 9. error - 错误事件
系统发生错误时发送的事件。

```json
{
  "event": "error",
  "messageId": "msg_abc123_1753810233047",
  "conversationId": "conv_456",
  "content": "LLM调用超时，请重试",
  "data": {
    "errorCode": "LLM_TIMEOUT",
    "canRetry": true,
    "retryCount": 1,
    "maxRetries": 3,
    "status": 500
  },
  "createdAt": 1753810240000
}
```

## 🔄 完整事件执行流程

### 营销工作流执行顺序

营销系统采用基于LangGraph的智能工作流，根据用户输入动态路由到不同的Agent节点。以下是典型的执行流程：

#### 1. 工作流启动阶段
```
workflowStarted → 工作流开始执行
```

#### 2. 智能路由阶段
```
nodeStarted (ROUTER_AGENT) → 开始智能路由分析
nodeProgress (ROUTER_AGENT) → 正在分析您的营销需求，请稍候...
nodeProgress (ROUTER_AGENT) → 需求分析完成，接下来将深入了解您的具体需求
nodeEnd (ROUTER_AGENT) → 路由分析完成
```

#### 3. 意图识别阶段
```
nodeStarted (INTENTION_AGENT) → 开始意图识别
nodeProgress (INTENTION_AGENT) → 正在深入理解您的营销目标...
nodeProgress (INTENTION_AGENT) → 正在分析活动类型和目标受众...
message × N → LLM推理过程的流式输出（打字机效果）
nodeEnd (INTENTION_AGENT) → 意图识别完成
```

#### 4. 客群分析阶段（如需要）
```
nodeStarted (TAG_CUSTOMER_AGENT) → 开始客群分析
nodeProgress (TAG_CUSTOMER_AGENT) → 正在分析目标客群特征...
message × N → 客群分析推理过程
nodeEnd (TAG_CUSTOMER_AGENT) → 客群分析完成
```

#### 5. 方案生成阶段
```
nodeStarted (PLAN_CREATOR_AGENT) → 开始方案生成
nodeProgress (PLAN_CREATOR_AGENT) → 正在制定营销方案...
message × N → 方案生成的流式输出
nodeEnd (PLAN_CREATOR_AGENT) → 方案生成完成
```

#### 6. 工作流结束阶段
```
workflowEnd → 工作流执行完成
messageEnd → 消息生成结束
```

### 中断处理流程

当系统需要用户补充信息时：

```
nodeStarted → nodeProgress → interrupt → (等待用户输入) → 恢复执行
```

### 错误处理流程

当系统发生错误时：

```
error → workflowEnd (status: "failed")
```

## 📊 事件统计信息

根据实际测试，典型的营销工作流会产生以下事件分布：

| 事件类型 | 数量范围 | 占比 | 说明 |
|---------|---------|------|------|
| message | 80-120 | ~90% | LLM推理过程的流式输出 |
| nodeProgress | 4-8 | ~5% | 节点执行进度更新 |
| nodeStarted | 2-5 | ~3% | 节点开始执行 |
| nodeEnd | 2-5 | ~2% | 节点执行完成 |
| workflowStarted | 1 | <1% | 工作流开始 |
| workflowEnd | 1 | <1% | 工作流结束 |

### 执行时间参考

- **简单需求**：5-15秒，50-80个事件
- **复杂需求**：15-30秒，100-150个事件
- **需要补充信息**：会产生interrupt事件，等待用户输入

## 🎯 Agent节点说明

### 1. ROUTER_AGENT - 智能路由
- **功能**：分析用户输入，决定执行路径
- **输出**：路由决策和置信度
- **下一步**：通常路由到INTENTION_AGENT

### 2. INTENTION_AGENT - 意图识别
- **功能**：深度理解用户营销意图
- **输出**：营销目标、活动类型、目标受众等
- **下一步**：根据信息完整度决定后续流程

### 3. TAG_CUSTOMER_AGENT - 客群分析
- **功能**：分析目标客群特征和偏好
- **输出**：客群画像、触达渠道建议
- **下一步**：通常进入方案生成

### 4. PLAN_CREATOR_AGENT - 方案生成
- **功能**：基于前面分析生成具体营销方案
- **输出**：完整的营销执行方案
- **下一步**：工作流结束



## 💻 前端集成示例

### JavaScript/TypeScript 集成

#### 基础SSE连接
```typescript
interface StreamEvent {
  event: string;
  messageId: string;
  conversationId: string;
  content: string;
  data: any;
  createdAt: number;
  runId?: string;
}

class MarketingStreamClient {
  private eventSource: EventSource | null = null;
  private messageBuffer: string = '';

  async startStream(userInput: string, userId: string, conversationId?: string) {
    const response = await fetch('/api/marketing/chat/stream', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        userInput,
        userId,
        conversationId
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const reader = response.body?.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader!.read();
      if (done) break;

      const chunk = decoder.decode(value);
      const lines = chunk.split('\n');

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          try {
            const eventData = JSON.parse(line.slice(6)) as StreamEvent;
            this.handleEvent(eventData);
          } catch (e) {
            console.error('解析事件数据失败:', e);
          }
        }
      }
    }
  }

  private handleEvent(event: StreamEvent) {
    switch (event.event) {
      case 'workflowStarted':
        this.onWorkflowStarted(event);
        break;
      case 'nodeStarted':
        this.onNodeStarted(event);
        break;
      case 'nodeProgress':
        this.onNodeProgress(event);
        break;
      case 'message':
        this.onMessage(event);
        break;
      case 'nodeEnd':
        this.onNodeEnd(event);
        break;
      case 'workflowEnd':
        this.onWorkflowEnd(event);
        break;
      case 'interrupt':
        this.onInterrupt(event);
        break;
      case 'error':
        this.onError(event);
        break;
    }
  }

  // 事件处理方法
  private onWorkflowStarted(event: StreamEvent) {
    console.log('工作流开始:', event.content);
    // 显示进度条或加载状态
  }

  private onNodeStarted(event: StreamEvent) {
    console.log('节点开始:', event.data.displayName);
    // 更新进度显示
  }

  private onNodeProgress(event: StreamEvent) {
    console.log('节点进度:', event.content);
    // 更新进度条
  }

  private onMessage(event: StreamEvent) {
    // 实现打字机效果
    this.messageBuffer += event.content;
    this.updateMessageDisplay(this.messageBuffer);
  }

  private onNodeEnd(event: StreamEvent) {
    console.log('节点完成:', event.data.displayName);
    // 更新完成状态
  }

  private onWorkflowEnd(event: StreamEvent) {
    console.log('工作流完成:', event.data);
    // 隐藏加载状态，显示完成状态
  }

  private onInterrupt(event: StreamEvent) {
    console.log('工作流中断:', event.content);
    // 显示用户输入界面
  }

  private onError(event: StreamEvent) {
    console.error('发生错误:', event.content);
    // 显示错误信息
  }

  private updateMessageDisplay(content: string) {
    // 更新消息显示区域
    const messageElement = document.getElementById('message-content');
    if (messageElement) {
      messageElement.innerHTML = this.markdownToHtml(content);
    }
  }

  private markdownToHtml(markdown: string): string {
    // 简单的Markdown转换，建议使用专业库如marked.js
    return markdown
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^# (.*$)/gim, '<h1>$1</h1>');
  }
}
```

---
