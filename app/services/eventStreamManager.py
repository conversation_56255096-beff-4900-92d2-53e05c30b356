"""
流式事件管理服务

负责处理所有类型的流式事件转换和格式化，包括：
1. LangGraph 原始事件处理
2. 不同事件模式的分发处理
3. 统一的事件格式化
4. 业务事件类型转换
"""

from typing import Dict, Any, Optional, Generator
from datetime import datetime
from loguru import logger

from app.utils.stream_events import StreamEventBuilder, StreamEventTypes, NodeStatus
from app.utils.common_utils import getNodeIndex, generateFriendlyMessage, shouldShowToUser
from app.utils.StreamLLMContentFilterUtils import StreamContentFilter


class EventStreamManager:
    """
    流式事件管理器
    
    提供统一的流式事件处理和转换服务。
    负责将 LangGraph 的原始事件转换为标准化的业务事件。
    """
    
    def __init__(self):
        """初始化事件流管理器"""
        self.logger = logger.bind(service="EventStreamManager")
        self.lastRunId = None
        self.contentFilter = StreamContentFilter()  # 初始化内容过滤器

    def handleStreamEvent(self, mode: str, data: Any, threadId: str, messageId: str, conversationId: str = "") -> Optional[Dict[str, Any]]:
        """
        统一处理不同类型的流式事件

        根据事件模式分发到对应的处理器，支持：
        1. messages: LLM生成的token流
        2. updates: 节点状态更新事件
        3. custom: 自定义业务事件

        Args:
            mode: 事件模式类型
            data: 事件数据内容
            threadId: 线程ID
            messageId: 消息ID
            conversationId: 对话ID
            currentNodeId: 当前执行的节点ID

        Returns:
            Optional[Dict[str, Any]]: 处理后的事件数据，None表示跳过
        """
        if mode == "messages":
            return self.handleLlmTokenEvent(data, threadId, messageId)
        elif mode == "updates":
            self.logger.debug(f"处理节点更新事件: {data}")
            events = list(self.handleNodeUpdatesEvent(data, threadId, messageId))
            return events[0] if events else None
        elif mode == "custom":
            self.logger.debug(f"处理自定义事件: {data}")
            return self.handleCustomEvent(data, messageId, conversationId)
        return None
        
    # 注意：processStreamEvent 方法已被 handleStreamEvent 替代，避免冗余
        
    def handleMessageEvent(self, messageData: Any, messageId: str, conversationId: str) -> Optional[Dict[str, Any]]:
        """
        处理LLM消息事件
        
        Args:
            messageData: LLM消息数据
            messageId: 消息ID
            conversationId: 对话ID
            
        Returns:
            Optional[Dict[str, Any]]: 处理后的消息事件
        """
        content = ""
        
        if isinstance(messageData, tuple) and len(messageData) >= 1:
            messageChunk = messageData[0]
            if hasattr(messageChunk, 'content'):
                content = str(messageChunk.content)
                
        # 使用统一的内容过滤
        if not content or not shouldShowToUser(content):
            return None
            
        return StreamEventBuilder.createMessageEvent(
            messageId=messageId,
            conversationId=conversationId,
            content=content
        )
        
    def handleNodeUpdateEvent(self, updateData: Dict[str, Any], messageId: str, conversationId: str) -> Optional[Dict[str, Any]]:
        """
        处理单个节点更新事件
        
        Args:
            updateData: 节点更新数据
            messageId: 消息ID
            conversationId: 对话ID
            
        Returns:
            Optional[Dict[str, Any]]: 处理后的节点事件
        """
        for nodeName, nodeOutput in updateData.items():
            # 过滤END节点
            if nodeName == "END":
                continue
                
            return StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId=conversationId,
                nodeId=nodeName,
                status="succeeded",
                stepNumber=getNodeIndex(nodeName),
                outputs=nodeOutput
            )
        return None
        
    def handleNodeUpdateEvents(self, updateData: Dict[str, Any], messageId: str, conversationId: str):
        """
        处理多个节点更新事件（生成器）
        
        Args:
            updateData: 节点更新数据
            messageId: 消息ID
            conversationId: 对话ID
            
        Yields:
            Dict[str, Any]: 节点更新事件
        """
        for nodeName, nodeOutput in updateData.items():
            # 过滤END节点
            if nodeName == "END":
                continue
                
            yield StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId=conversationId,
                nodeId=nodeName,
                status="succeeded",
                stepNumber=getNodeIndex(nodeName),
                outputs=nodeOutput
            )
            
    def handleCustomEvent(self, customData: Any, messageId: str, conversationId: str) -> Optional[Dict[str, Any]]:
        """
        处理自定义事件 - 支持业务流式输出
        
        Args:
            customData: 自定义事件数据
            messageId: 消息ID
            conversationId: 对话ID
            
        Returns:
            Optional[Dict[str, Any]]: 处理后的自定义事件
        """
        if isinstance(customData, dict):
            eventName = customData.get("event", "custom")
            
            # 1. 处理业务内容流式事件
            if eventName == "businessContentChunk":
                return StreamEventBuilder.createMessageEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    content=customData.get("content", ""),
                    contentType=customData.get("contentType"),
                    nodeId=customData.get("nodeId"),
                    runId=customData.get("runId")
                )
                
            # 2. 处理业务内容完成事件
            elif eventName == "businessContentComplete":
                return StreamEventBuilder.createMessageEndEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    contentType=customData.get("contentType"),
                    nodeId=customData.get("nodeId"),
                    fullContent=customData.get("fullContent"),
                    runId=customData.get("runId")
                )
                
            # 3. 处理思考过程事件
            elif eventName == "thinkingProcess":
                return StreamEventBuilder.createMessageEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    content=customData.get("content", ""),
                    contentType="thinkingProcess",
                    nodeId=customData.get("nodeId")
                )
                
            # 4. 处理节点进度事件
            elif eventName == "nodeProgress" or customData.get("type") == "nodeProgress":
                return StreamEventBuilder.createNodeProgressEvent(
                    messageId=messageId,
                    conversationId=conversationId,
                    nodeId=customData.get("nodeId", "UNKNOWN_AGENT"),
                    content=customData.get("content", "处理中..."),
                    progress=customData.get("progress", 50),
                    stage=customData.get("stage", "processing")
                )

            # 5. 处理其他自定义事件
            else:
                eventType = customData.get("type", "custom")
                content = customData.get("content", customData.get("message", "自定义事件"))

                return {
                    "event": eventType,
                    "messageId": messageId,
                    "conversationId": conversationId,
                    "content": content,
                    "data": customData.get("data", customData),
                    "createdAt": int(datetime.now().timestamp() * 1000)
                }
        return None

    def handleLlmTokenEvent(self, messageData, threadId: str, messageId: str) -> Optional[Dict[str, Any]]:
        """
        处理LLM生成的token，实现打字机效果

        Args:
            messageData: LLM消息数据 (通常是tuple: (AIMessageChunk, metadata))
            threadId: 线程ID
            messageId: 消息ID

        Returns:
            Optional[Dict[str, Any]]: LLM token事件
        """
        # 1. 提取token内容、runId和nodeId
        content = ""
        runId = ""
        nodeId = "UNKNOWN_AGENT"

        # LangGraph传递的是tuple: (AIMessageChunk, metadata)
        if isinstance(messageData, tuple) and len(messageData) >= 2:
            messageChunk = messageData[0]
            metadata = messageData[1]

            # 从消息块提取内容和runId
            if hasattr(messageChunk, 'content'):
                content = str(messageChunk.content)
            if hasattr(messageChunk, 'id'):
                runId = str(messageChunk.id)

            # 从metadata提取nodeId
            if isinstance(metadata, dict) and 'langgraph_node' in metadata:
                nodeId = metadata['langgraph_node']

        elif isinstance(messageData, tuple) and len(messageData) >= 1:
            messageChunk = messageData[0]
            if hasattr(messageChunk, 'content'):
                content = str(messageChunk.content)
            if hasattr(messageChunk, 'id'):
                runId = str(messageChunk.id)
        # 处理其他类型的消息数据
        elif hasattr(messageData, 'content'):
            content = str(messageData.content)
            if hasattr(messageData, 'id'):
                runId = str(messageData.id)
        elif isinstance(messageData, dict):
            content = messageData.get('content', '')
            runId = messageData.get('id', '')
        elif isinstance(messageData, str):
            content = messageData
        else:
            # 尝试转换为字符串
            content = str(messageData)

        # 2. 检测新的LLM调用，重置thinking状态
        if runId and runId != self.lastRunId:
            if self.contentFilter:
                self.contentFilter.resetForNewCall()
            self.lastRunId = runId

        # 3. 过滤空内容和结束标记
        if not content or content.strip() == "":
            return None

        # 4. 过滤工具调用相关的技术内容
        if self.isToolCallContent(messageData):
            self.logger.debug(f"过滤工具调用内容: runId={runId}")
            return None

        # 5. 严格过滤：提取标签内容并获取标签类型
        if self.contentFilter:
            filteredContent, contentType = self.contentFilter.filterContent(content)
            if not filteredContent:
                return None
            content = filteredContent
        else:
            contentType = None

        # 6. 使用增强的消息事件构建器
        return StreamEventBuilder.createMessageEvent(
            messageId=messageId,
            conversationId="",  # 将在上层填充
            content=content,
            contentType=contentType,
            nodeId=nodeId,  # 使用从messageData中提取的nodeId
            runId=runId if runId else None
        )

    def handleNodeUpdatesEvent(self, updateData: Dict[str, Any], threadId: str, messageId: str) -> Generator[Dict[str, Any], None, None]:
        """
        处理节点更新事件，提供丰富的业务信息

        Args:
            updateData: 节点更新数据
            threadId: 线程ID（仅内部使用）
            messageId: 消息ID

        Yields:
            Dict[str, Any]: 统一结构的节点事件（content + data）
        """
        for nodeName, nodeOutput in updateData.items():
            # 1. 过滤END节点，它不是实际的业务节点
            if nodeName == "END":
                self.logger.debug(f"跳过END节点的事件发送: {nodeName}")
                continue

            # 2. 处理不同类型的nodeOutput
            if isinstance(nodeOutput, tuple):
                # 如果是tuple，可能是(state, metadata)格式，取第一个元素
                actualOutput = nodeOutput[0] if nodeOutput else {}
            elif isinstance(nodeOutput, dict):
                actualOutput = nodeOutput
            else:
                # 其他类型，包装成字典
                actualOutput = {"result": nodeOutput}

            # 3. 发送节点完成事件，使用用户友好的消息
            friendlyCompleteMessage = generateFriendlyMessage(nodeName, 'complete')

            nodeFinishedEvent = StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId="",  # 将在上层填充
                nodeId=nodeName,
                status=NodeStatus.SUCCEEDED,
                stepNumber=getNodeIndex(nodeName),
                elapsedTime=0.0,  # 可以计算实际执行时间
                outputs=actualOutput
            )

            # 覆盖默认的content为用户友好消息
            nodeFinishedEvent["content"] = friendlyCompleteMessage
            yield nodeFinishedEvent

            # 4. 如果需要用户输入，发送引导事件
            if isinstance(actualOutput, dict) and actualOutput.get("needCompletion", False):
                userGuidanceEvent = self.generateUserGuidanceEvent(
                    nodeName, actualOutput, messageId
                )
                if userGuidanceEvent:
                    yield userGuidanceEvent

    def isToolCallContent(self, messageData) -> bool:
        """
        检查是否是工具调用内容

        Args:
            messageData: 消息数据

        Returns:
            bool: 是否是工具调用内容
        """
        # 简化的工具调用检测逻辑
        if isinstance(messageData, tuple) and len(messageData) >= 1:
            chunk = messageData[0]
            if hasattr(chunk, 'tool_calls') and chunk.tool_calls:
                return True
        return False

    def getCurrentExecutingNode(self) -> str:
        """
        获取当前正在执行的节点ID

        Returns:
            str: 当前执行的节点ID，如果没有则返回UNKNOWN_AGENT
        """
        return getattr(self, 'currentExecutingNode', 'UNKNOWN_AGENT')

    def generateUserGuidanceEvent(self, nodeName: str, actualOutput: Dict[str, Any], messageId: str) -> Optional[Dict[str, Any]]:
        """
        生成用户引导事件

        Args:
            nodeName: 节点名称
            actualOutput: 节点输出
            messageId: 消息ID

        Returns:
            Optional[Dict[str, Any]]: 用户引导事件
        """
        # 简化的用户引导事件生成逻辑
        guidanceMessage = actualOutput.get("guidanceMessage", "请提供更多信息")
        missingFields = actualOutput.get("missingFields", [])

        return StreamEventBuilder.createUserGuidanceEvent(
            messageId=messageId,
            conversationId="",
            guidanceType="completion",
            guidanceMessage=guidanceMessage,
            missingFields=missingFields
        )

    def sendMessageEndEvent(self, messageId: str, runId: str) -> Optional[Dict[str, Any]]:
        """
        发送LLM消息结束事件

        在每次LLM思考完成后发送，而不是在整个工作流结束后

        Args:
            messageId: 消息ID
            runId: LLM运行ID

        Returns:
            Optional[Dict[str, Any]]: 消息结束事件
        """
        return StreamEventBuilder.createMessageEndEvent(
            messageId=messageId,
            conversationId="",  # 将在上层填充
            contentType="llm_response",
            runId=runId,
            metadata={
                "llmCallCompleted": True,
                "runId": runId
            }
        )
