"""
流式处理服务

负责处理流式响应的核心业务逻辑，包括：
1. 流式工作流执行协调
2. 事件流管理和转换
3. 中断恢复处理
4. 流式事件的统一格式化
"""

import json
from datetime import datetime
from typing import Dict, Any, AsyncGenerator, Optional
from loguru import logger

from app.api.models import MarketingChatRequest
from app.workflows.MarketingWorkflow import createWorkflow
from app.services.conversationService import ConversationService
from app.services.marketingChatService import WorkflowContext
from app.services.eventStreamManager import EventStreamManager
from app.utils.stream_events import StreamEventBuilder, StreamEventTypes, WorkflowStatus


class StreamingService:
    """
    流式处理服务
    
    提供流式响应的完整业务逻辑处理。
    负责协调流式工作流执行、事件管理、中断恢复等功能。
    """
    
    def __init__(self):
        """初始化流式处理服务"""
        self.logger = logger.bind(service="StreamingService")
        self.workflow = createWorkflow()
        self.conversationService = ConversationService()
        self.eventStreamManager = EventStreamManager()
        
    async def processChatStream(self, request: MarketingChatRequest, 
                               context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理流式营销对话请求
        
        Args:
            request: 营销对话请求
            context: 工作流执行上下文
            
        Yields:
            Dict[str, Any]: 流式事件数据
        """
        try:
            # 1. 检查是否为中断恢复场景
            isResumeScenario = await self.checkInterruptedWorkflow(context.threadId)
            
            # 2. 执行流式工作流
            if isResumeScenario:
                self.logger.info(f"检测到中断恢复场景: {context.threadId}")
                async for event in self.executeResumeWorkflow(request, context):
                    yield event
            else:
                async for event in self.executeStreamWorkflow(request, context):
                    yield event
                    
        except Exception as e:
            # 3. 处理流式执行异常
            async for event in self.handleStreamException(e, request, context):
                yield event
                
    async def checkInterruptedWorkflow(self, threadId: str) -> bool:
        """
        检查工作流是否处于中断状态
        
        Args:
            threadId: 线程ID
            
        Returns:
            bool: 是否处于中断状态
        """
        try:
            return await self.workflow.isInterruptedWorkflow(threadId)
        except Exception as e:
            self.logger.debug(f"检查中断状态失败: {e}")
            return False
            
    async def executeStreamWorkflow(self, request: MarketingChatRequest, 
                                   context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        执行正常流式工作流
        
        Args:
            request: 营销对话请求
            context: 工作流执行上下文
            
        Yields:
            Dict[str, Any]: 流式事件数据
        """
        startTime = int(datetime.now().timestamp())
        eventCount = 0
        
        try:
            self.logger.info(f"开始流式执行工作流: {context.threadId}")
            
            # 使用工作流的流式执行方法，直接传递完整上下文
            async for event in self.workflow.executeStreamWorkflow(context):
                # 使用事件流管理器处理事件
                processedEvent = self.processEventWithManager(event, context)
                if processedEvent:
                    eventCount += 1
                    yield processedEvent
                    
            # 发送工作流完成事件
            endTime = int(datetime.now().timestamp())
            elapsedTime = endTime - startTime
            
            completionEvent = StreamEventBuilder.createWorkflowEndEvent(
                messageId=context.messageId,
                conversationId=context.conversationId,
                status=WorkflowStatus.SUCCEEDED,
                totalSteps=5,
                completedSteps=5,
                elapsedTime=elapsedTime
            )
            yield completionEvent
            
            # 注意：消息结束事件现在由 EventStreamManager 在每次LLM思考结束后自动发送
            
            self.logger.info(f"流式工作流执行完成: {context.threadId}, 共处理 {eventCount} 个事件")
            
            # 更新对话状态
            workflowResult = {
                "success": True,
                "status": "completed",
                "threadId": context.threadId,
                "eventCount": eventCount,
                "executionTime": elapsedTime
            }
            self.conversationService.updateAfterWorkflow(
                request, workflowResult, context, isStreamRequest=True
            )
            
        except Exception as e:
            self.logger.error(f"流式工作流执行异常: {str(e)}")
            raise
            
    async def executeResumeWorkflow(self, request: MarketingChatRequest, 
                                   context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        执行中断恢复工作流
        
        Args:
            request: 营销对话请求
            context: 工作流执行上下文
            
        Yields:
            Dict[str, Any]: 流式事件数据
        """
        try:
            # 中断恢复流程，直接传递完整上下文
            async for event in self.workflow.resumeInterruptedWorkflowSimplified(context):
                # 使用事件流管理器处理事件
                processedEvent = self.processEventWithManager(event, context)
                if processedEvent:
                    yield processedEvent
                    
            self.logger.info(f"中断恢复工作流执行完成: {context.threadId}")
            
        except Exception as e:
            self.logger.error(f"中断恢复工作流执行异常: {str(e)}")
            raise
            
    def formatStreamEvent(self, event: Dict[str, Any], context: WorkflowContext) -> Optional[Dict[str, Any]]:
        """
        格式化流式事件
        
        Args:
            event: 原始事件数据
            context: 工作流执行上下文
            
        Returns:
            Optional[Dict[str, Any]]: 格式化后的事件数据
        """
        if not event or not isinstance(event, dict):
            return None
            
        # 确保事件包含必要的标识信息
        if "conversationId" not in event or not event["conversationId"]:
            event["conversationId"] = context.conversationId
            
        if "messageId" not in event or not event["messageId"]:
            event["messageId"] = context.messageId
            
        return event

    def processEventWithManager(self, event: Dict[str, Any], context: WorkflowContext) -> Optional[Dict[str, Any]]:
        """
        使用事件流管理器处理事件

        Args:
            event: 原始事件数据
            context: 工作流执行上下文

        Returns:
            Optional[Dict[str, Any]]: 处理后的事件数据
        """
        if not event or not isinstance(event, dict):
            return None

        # 1. 检查是否是LangGraph的标准流式事件格式
        if "mode" in event and "data" in event:
            # 使用事件流管理器处理
            processedEvent = self.eventStreamManager.handleStreamEvent(
                event["mode"],
                event["data"],
                context.threadId,
                context.messageId,
                context.conversationId,
            )

            if processedEvent:
                # 确保事件包含必要的标识信息
                processedEvent["conversationId"] = context.conversationId
                processedEvent["messageId"] = context.messageId

            return processedEvent

        # 2. 直接格式化其他类型的事件
        return self.formatStreamEvent(event, context)
        
    async def handleStreamException(self, exception: Exception, 
                                   request: MarketingChatRequest,
                                   context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        处理流式执行异常
        
        Args:
            exception: 异常对象
            request: 营销对话请求
            context: 工作流执行上下文
            
        Yields:
            Dict[str, Any]: 异常处理事件
        """
        if self.isInterruptException(exception):
            # 处理中断异常
            self.logger.info(f"流式工作流被中断: {context.threadId}")
            
            # 更新用户记忆和对话状态（中断情况）
            interruptResult = {
                "success": True,
                "data": {"status": "interrupted", "message": "等待用户补充信息"},
                "threadId": context.threadId,
                "status": "interrupted"
            }
            self.conversationService.updateAfterWorkflow(
                request, interruptResult, context, isStreamRequest=True
            )
            
            # 发送中断事件
            interruptEvent = {
                "eventType": "workflowInterrupted",
                "messageId": context.messageId,
                "message": "工作流已中断，等待用户补充信息",
                "threadId": context.threadId,
                "conversationId": context.conversationId,
                "userId": context.workflowInput.get("userId"),
                "timestamp": datetime.now().isoformat(),
                "priority": "high",
                "category": "system",
                "userFriendlyData": {
                    "status": "interrupted",
                    "statusText": "需要更多信息才能继续",
                    "showContinue": True,
                    "canResume": True
                }
            }
            yield interruptEvent
            
        else:
            # 处理一般异常
            self.logger.error(f"流式工作流执行失败: {str(exception)}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            
            # 更新用户记忆和对话状态（错误情况）
            errorResult = {
                "success": False,
                "error": str(exception),
                "threadId": context.threadId,
                "status": "error"
            }
            self.conversationService.updateAfterWorkflow(
                request, errorResult, context, isStreamRequest=True
            )
            
            # 发送错误事件
            errorEvent = {
                "event": "error",
                "messageId": context.messageId,
                "conversationId": context.conversationId,
                "taskId": context.threadId,
                "status": 500,
                "code": "workflow_execution_error",
                "message": f"回复生成失败: {str(exception)}",
                "createdAt": int(datetime.now().timestamp())
            }
            yield errorEvent
            
    def isInterruptException(self, exception: Exception) -> bool:
        """
        检查是否是中断异常
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否是中断异常
        """
        interruptKeywords = [
            "需要用户输入",
            "信息不完整", 
            "缺少必要信息",
            "user_input_required",
            "incomplete_information",
            "interrupt"
        ]
        
        exceptionStr = str(exception).lower()
        return any(keyword.lower() in exceptionStr for keyword in interruptKeywords)
        
    def createJsonSerializer(self):
        """
        创建JSON序列化器，处理特殊对象
        
        Returns:
            Callable: JSON序列化函数
        """
        def jsonSerializer(obj):
            # 处理LangGraph的Interrupt对象
            if hasattr(obj, '__class__') and 'Interrupt' in obj.__class__.__name__:
                return {
                    "type": "Interrupt",
                    "message": str(obj) if str(obj) else "Workflow interrupted"
                }
                
            # 处理其他不可序列化的对象
            try:
                return str(obj)
            except:
                return f"<{type(obj).__name__} object>"
                
        return jsonSerializer
        
    async def generateSseStream(self, eventGenerator: AsyncGenerator[Dict[str, Any], None]) -> AsyncGenerator[str, None]:
        """
        生成SSE格式的流式数据
        
        Args:
            eventGenerator: 事件生成器
            
        Yields:
            str: SSE格式的数据
        """
        jsonSerializer = self.createJsonSerializer()
        
        try:
            async for event in eventGenerator:
                try:
                    eventJson = json.dumps(event, ensure_ascii=False, default=jsonSerializer)
                    yield f"data: {eventJson}\n\n"
                except Exception as serializationError:
                    self.logger.error(f"事件序列化失败: {str(serializationError)}")
                    # 发送错误事件但不中断流
                    errorEvent = {
                        "event": "error",
                        "message": f"事件序列化失败: {str(serializationError)}",
                        "timestamp": int(datetime.now().timestamp())
                    }
                    try:
                        errorJson = json.dumps(errorEvent, ensure_ascii=False)
                        yield f"data: {errorJson}\n\n"
                    except:
                        # 如果连错误事件都无法序列化，发送简单的错误信息
                        yield f"data: {{'event': 'error', 'message': 'Serialization failed'}}\n\n"
                        
        except GeneratorExit:
            # 处理客户端断开连接的情况
            self.logger.info("客户端断开连接")
            return
        except Exception as e:
            self.logger.error(f"SSE流生成异常: {str(e)}")
            # 发送最终错误事件
            finalErrorEvent = {
                "event": "error",
                "message": f"流式处理异常: {str(e)}",
                "timestamp": int(datetime.now().timestamp())
            }
            try:
                errorJson = json.dumps(finalErrorEvent, ensure_ascii=False)
                yield f"data: {errorJson}\n\n"
            except:
                yield f"data: {{'event': 'error', 'message': 'Stream processing failed'}}\n\n"
