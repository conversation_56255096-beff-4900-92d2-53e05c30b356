"""
营销对话服务

负责处理营销对话的核心业务逻辑，包括：
1. 请求验证和数据准备
2. 工作流执行协调
3. 响应格式化
4. 业务规则处理
"""

import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from loguru import logger

from app.api.models import MarketingChatRequest, MarketingChatResponse
from app.core.MemoryManager import MemoryManager
from app.services.conversationService import ConversationService
from langchain_core.messages import HumanMessage, AIMessage

from app.utils.models import WorkflowContext
from app.workflows.MarketingWorkflow import createWorkflow





class MarketingChatService:
    """
    营销对话核心业务服务
    
    提供营销对话的完整业务逻辑处理，包括同步和异步模式。
    负责协调工作流执行、内存管理、状态维护等核心功能。
    """
    
    def __init__(self):
        """初始化营销对话服务"""
        self.logger = logger.bind(service="MarketingChatService")
        self.memoryManager = MemoryManager()
        self.conversationService = ConversationService(self.memoryManager)
        self.workflow = createWorkflow()
        
    def validateRequest(self, request: MarketingChatRequest) -> tuple[bool, Optional[str]]:
        """
        验证请求参数
        
        Args:
            request: 营销对话请求
            
        Returns:
            tuple[bool, Optional[str]]: (是否有效, 错误信息)
        """
        # 1. 验证必填字段
        if not request.userId:
            return False, "userId不能为空"
            
        if not request.userInput or not request.userInput.strip():
            return False, "userInput不能为空"
            
        # 2. 验证字段长度
        if len(request.userInput) > 2000:
            return False, "userInput长度不能超过2000字符"
            
        # 3. 验证用户ID格式
        if len(request.userId) > 100:
            return False, "userId长度不能超过100字符"
            
        return True, None
        
    def prepareWorkflowContext(self, request: MarketingChatRequest) -> WorkflowContext:
        """
        准备工作流执行上下文
        
        Args:
            request: 营销对话请求
            
        Returns:
            WorkflowContext: 工作流执行上下文
        """
        # 1. 生成或获取会话标识
        conversationId = request.conversationId or str(uuid.uuid4())
        threadId = f"{request.userId}_{conversationId}"
        sessionId = request.sessionId or str(uuid.uuid4())
        messageId = f"msg_{str(uuid.uuid4())[:8]}_{int(datetime.now().timestamp())}"
        
        # 2. 获取用户记忆
        userMemory = self.memoryManager.getUserMemory(request.userId)
        
        # 3. 检查现有工作流状态
        config = {"configurable": {"thread_id": threadId}}
        existingState = self.getExistingWorkflowState(config)
        
        # 4. 构建工作流输入
        workflowInput = self.buildWorkflowInput(
            request, sessionId, userMemory, existingState
        )
        
        return WorkflowContext(
            workflowInput=workflowInput,
            config=config,
            conversationId=conversationId,
            threadId=threadId,
            sessionId=sessionId,
            messageId=messageId,
            userMemory=userMemory
        )
        
    def getExistingWorkflowState(self, config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        获取现有工作流状态
        
        Args:
            config: 工作流配置
            
        Returns:
            Optional[Dict[str, Any]]: 现有状态或None
        """
        try:
            return self.workflow.app.get_state(config)
        except Exception as e:
            self.logger.warning(f"获取工作流状态失败: {e}")
            return None
            
    def buildWorkflowInput(self, 
                          request: MarketingChatRequest,
                          sessionId: str,
                          userMemory: Dict[str, Any],
                          existingState: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """
        构建工作流输入数据
        
        Args:
            request: 营销对话请求
            sessionId: 会话ID
            userMemory: 用户记忆
            existingState: 现有状态
            
        Returns:
            Dict[str, Any]: 工作流输入数据
        """
        baseInput = {
            "messages": [HumanMessage(content=request.userInput)],
            "userInput": request.userInput,
            "userId": request.userId,
            "sessionId": sessionId,
            "userProfile": userMemory.get("profile", {}),
            "userMemoryContext": {
                "recentHistory": userMemory.get("history", [])[-10:],
                "preferences": userMemory.get("preferences", {}),
                "tags": userMemory.get("tags", [])
            }
        }
        
        # 如果是新会话，添加初始化字段
        if not existingState or not existingState.values:
            baseInput.update({
                "workflowId": str(uuid.uuid4()),
                "currentStep": "start",
                "needCompletion": False,
                "guidancePrompts": {},
                "fieldSuggestions": {},
                "followupQuestions": {}
            })
            
        return baseInput
        
    async def processChat(self, request: MarketingChatRequest) -> MarketingChatResponse:
        """
        处理营销对话请求（同步模式）
        
        Args:
            request: 营销对话请求
            
        Returns:
            MarketingChatResponse: 营销对话响应
        """
        # 1. 验证请求
        isValid, errorMessage = self.validateRequest(request)
        if not isValid:
            return self.createErrorResponse(errorMessage, request)
            
        # 2. 准备工作流上下文
        context = self.prepareWorkflowContext(request)
        
        # 3. 执行工作流
        try:
            self.logger.info(f"开始执行同步工作流，threadId: {context.threadId}")
            result = self.workflow.app.invoke(context.workflowInput, config=context.config)
            self.logger.info(f"同步工作流执行完成，threadId: {context.threadId}")
            
            workflowResult = self.processWorkflowResult(result, context)
            
        except Exception as e:
            workflowResult = self.handleWorkflowException(e, context)
            
        # 4. 更新用户记忆和对话状态
        self.conversationService.updateAfterWorkflow(request, workflowResult, context)
        
        # 5. 格式化响应
        return self.formatSyncResponse(workflowResult, context)
        
    def processWorkflowResult(self, result: Any, context: WorkflowContext) -> Dict[str, Any]:
        """
        处理工作流执行结果
        
        Args:
            result: 工作流原始结果
            context: 工作流上下文
            
        Returns:
            Dict[str, Any]: 处理后的结果
        """
        if isinstance(result, dict) and "messages" in result:
            # 从消息历史中提取AI回复
            aiMessages = [msg for msg in result["messages"] if isinstance(msg, AIMessage)]
            latestAiResponse = aiMessages[-1].content if aiMessages else "处理完成"
            
            return {
                "success": True,
                "data": {
                    "response": latestAiResponse,
                    "status": result.get("currentStep", "completed"),
                    "needCompletion": result.get("needCompletion", False),
                    "missingFields": result.get("missingFields", {}),
                    "guidancePrompts": result.get("guidancePrompts", {}),
                    "fieldSuggestions": result.get("fieldSuggestions", {}),
                    "followupQuestions": result.get("followupQuestions", {}),
                    "intentInfo": result.get("intentInfo", {}),
                    "planDraft": result.get("planDraft", {}),
                },
                "threadId": context.threadId,
                "status": "completed"
            }
        else:
            return result
            
    def handleWorkflowException(self, exception: Exception, context: WorkflowContext) -> Dict[str, Any]:
        """
        处理工作流执行异常
        
        Args:
            exception: 异常对象
            context: 工作流上下文
            
        Returns:
            Dict[str, Any]: 异常处理结果
        """
        if self.isInterruptException(exception):
            self.logger.info(f"工作流被中断: {context.threadId}")
            return {
                "success": True,
                "data": {"status": "interrupted", "message": "等待用户补充信息"},
                "threadId": context.threadId,
                "status": "interrupted"
            }
        else:
            # 记录详细错误信息
            import traceback
            errorDetails = traceback.format_exc()
            self.logger.error(f"工作流执行失败: {str(exception)}")
            self.logger.error(f"详细错误信息: {errorDetails}")
            
            return {
                "success": False,
                "error": str(exception),
                "threadId": context.threadId
            }
            
    def isInterruptException(self, exception: Exception) -> bool:
        """
        检查是否是中断异常
        
        Args:
            exception: 异常对象
            
        Returns:
            bool: 是否是中断异常
        """
        return (
            "interrupt" in str(exception).lower() or
            "Interrupt" in str(type(exception).__name__) or
            hasattr(exception, '__class__') and 'interrupt' in exception.__class__.__name__.lower()
        )
        
    def createErrorResponse(self, errorMessage: str, request: MarketingChatRequest) -> MarketingChatResponse:
        """
        创建错误响应
        
        Args:
            errorMessage: 错误信息
            request: 原始请求
            
        Returns:
            MarketingChatResponse: 错误响应
        """
        return MarketingChatResponse(
            success=False,
            data={"error": errorMessage},
            message=errorMessage,
            conversationId=request.conversationId or "",
            sessionId=request.sessionId or ""
        )
        
    def formatSyncResponse(self, workflowResult: Dict[str, Any], context: WorkflowContext) -> MarketingChatResponse:
        """
        格式化同步响应
        
        Args:
            workflowResult: 工作流结果
            context: 工作流上下文
            
        Returns:
            MarketingChatResponse: 格式化后的响应
        """
        # 特殊处理方案生成完成的情况
        if isinstance(workflowResult, dict) and workflowResult.get("workflowStatus") == "plan_generated":
            message = workflowResult.get("message", "方案生成完成，请查看并确认")
            status = "plan_ready"
        else:
            message = "处理完成" if workflowResult.get("success", True) else workflowResult.get("error", "处理失败")
            status = workflowResult.get("status", "completed")
            
        return MarketingChatResponse(
            success=workflowResult.get("success", True),
            data={
                "status": status,
                "result": workflowResult.get("data", workflowResult),
                "conversationId": context.conversationId,
                "planDraft": workflowResult.get("planDraft") if workflowResult.get("workflowStatus") == "plan_generated" else None,
                "nextAction": workflowResult.get("nextAction", "none")
            },
            message=message,
            conversationId=context.conversationId,
            sessionId=context.sessionId
        )
