"""
智能营销工作流，基于LangGraph的多Agent协作系统

适用于API服务/前后端分离/线上部署场景。
"""

import os
import sys

from langgraph.checkpoint.memory import MemorySaver
from langgraph.types import interrupt, Command

from app.core.checkpointer import getDefaultSaver

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
import uuid
from langgraph.graph import StateGraph
from typing import Dict, Any, AsyncGenerator, Generator

from app.utils.models import MarketingState, WorkflowContext
from datetime import datetime

# 导入所有Agent
from app.agents.RouterAgent import RouterAgent
from app.agents.IntentionAgent import IntentionAgent
from app.agents.TagCustomerAgent import TagCustomerAgent
from app.agents.PlanCreatorAgent import PlanCreatorAgent
from loguru import logger as loguru_logger
from app.utils.stream_events import StreamEventBuilder, WorkflowStatus
from app.utils.common_utils import getNodeDisplayName, getNodeIndex, ResponseFormatter
from app.utils.StreamLLMContentFilterUtils import StreamContentFilter


class MarketingWorkflow:
    """
    智能营销工作流

    基于LangGraph的多Agent协作系统，实现从用户输入到营销方案生成的完整流程。
    适用于API服务/前后端分离/线上部署场景。

    主要功能:
    - 智能路由决策
    - 意图识别与补全
    - 客群分析与标签
    - 营销方案生成
    - 流式响应处理
    """

    def __init__(self):
        """初始化营销工作流"""
        self.logger = loguru_logger
        self._initializeComponents()
        self.logger.info("🔄 营销工作流初始化完成")

    def _initializeComponents(self):
        """初始化所有组件"""
        # 初始化Agent
        self._initializeAgents()

        # 构建工作流
        self.workflow = self._buildWorkflow()

        # 配置状态管理器
        self._setupCheckpointer()

        # 编译工作流
        self._compileWorkflow()

        # 初始化其他组件
        self._setupAdditionalComponents()

    def _setupCheckpointer(self):
        """配置状态管理器"""
        try:
            self.checkpointer = getDefaultSaver()
            self.logger.info(f"状态管理器初始化成功: {type(self.checkpointer).__name__}")
        except Exception as e:
            self.checkpointer = MemorySaver()
            self.logger.warning(f"使用内存状态管理器: {str(e)}")

    def _compileWorkflow(self):
        """编译工作流"""
        try:
            self._activeCallbacks = {}
            self.app = self.workflow.compile(checkpointer=self.checkpointer)
            self.logger.info("🔄 营销工作流编译成功")
        except Exception as e:
            self.logger.error(f"❌ 营销工作流编译失败: {str(e)}")
            # 降级处理：使用无状态模式
            try:
                self.app = self.workflow.compile()
                self.logger.warning("⚠️ 使用无状态模式编译工作流")
            except Exception as e2:
                self.logger.error(f"❌ 无状态模式编译也失败: {str(e2)}")
                raise e2

    def _setupAdditionalComponents(self):
        """设置其他组件"""
        self.currentExecutingNode = None
        self.contentFilter = StreamContentFilter()

    def _initializeAgents(self) -> None:
        """初始化所有Agent实例"""
        try:
            # 智能路由Agent - 负责流程控制和路由决策
            self.routerAgent = RouterAgent(enableLlmRouting=True)

            # 意图识别Agent - 负责理解用户营销意图
            self.intentAgent = IntentionAgent()

            # 客群标签Agent - 负责分析目标客户群体
            self.customerAgent = TagCustomerAgent()

            # 方案生成Agent - 负责创建营销方案
            self.planGenAgent = PlanCreatorAgent()

            self.logger.info("🤖 所有Agent初始化完成")
        except Exception as e:
            self.logger.error(f"Agent初始化失败: {str(e)}")
            raise

    def _buildWorkflow(self) -> StateGraph:
        """构建营销工作流"""
        workflow = StateGraph(MarketingState)

        # 注册核心Agent节点
        self._registerAgentNodes(workflow)

        # 注册补全节点
        self._registerCompletionNodes(workflow)

        # 注册结束节点
        self._registerEndNode(workflow)

        # 设置入口点
        workflow.set_entry_point("ROUTER_AGENT")

        # 配置节点间的路由关系
        self._configureWorkflowEdges(workflow)

        return workflow

    def _registerAgentNodes(self, workflow: StateGraph):
        """注册Agent节点"""
        workflow.add_node("ROUTER_AGENT", self.createNodeWrapper("ROUTER_AGENT", self.routerAgent.execute))
        workflow.add_node("INTENTION_AGENT", self.createNodeWrapper("INTENTION_AGENT", self.intentAgent.execute))
        workflow.add_node("TAG_CUSTOMER_AGENT", self.createNodeWrapper("TAG_CUSTOMER_AGENT", self.customerAgent.execute))
        workflow.add_node("PLAN_CREATOR_AGENT", self.createNodeWrapper("PLAN_CREATOR_AGENT", self.planGenAgent.execute))

    def _registerCompletionNodes(self, workflow: StateGraph):
        """注册补全节点"""
        workflow.add_node("COMPLETE_INTENT_NODE", self.createNodeWrapper("COMPLETE_INTENT_NODE", self._completeIntentNode))
        workflow.add_node("COMPLETE_CUSTOMER_NODE", self.createNodeWrapper("COMPLETE_CUSTOMER_NODE", self._completeCustomerNode))
        workflow.add_node("COMPLETE_PLAN_NODE", self.createNodeWrapper("COMPLETE_PLAN_NODE", self._completePlanNode))
    def _registerEndNode(self, workflow: StateGraph):
        """注册结束节点"""
        def endNode(state):
            """工作流结束节点 - 返回最终结果"""
            planDraft = state.get("planDraft", {})
            workflowStatus = state.get("workflowStatus", "completed")

            return ResponseFormatter.format_workflow_result({
                "success": True,
                "workflowStatus": workflowStatus,
                "planDraft": planDraft,
                "nextAction": state.get("nextAction", "none"),
                "message": "方案生成完成，请查看并确认" if workflowStatus == "plan_generated" else "工作流执行完成"
            })

        workflow.add_node("END", endNode)
    def _configureWorkflowEdges(self, workflow: StateGraph):
        """配置工作流节点间的路由关系"""
        # 配置路由Agent的条件分支
        self._configureRouterEdges(workflow)

        # 配置意图Agent的条件分支
        self._configureIntentionEdges(workflow)

        # 配置客群Agent的条件分支
        self._configureCustomerEdges(workflow)

        # 配置方案Agent的条件分支
        self._configurePlanEdges(workflow)

        # 配置补全节点的直接边
        self._configureCompletionEdges(workflow)

    def _configureRouterEdges(self, workflow: StateGraph):
        """配置路由Agent的条件边"""
        def routerNextSelector(state):
            """路由Agent的下一步选择器"""
            try:
                nextAgent = state.get("nextNode")
                routingConfidence = state.get("routingConfidence", 0.0)
                routingReasoning = state.get("routingReasoning", "未知原因")

                # 有效的下一步节点
                validAgents = [
                    "INTENTION_AGENT", "COMPLETE_INTENT_NODE",
                    "TAG_CUSTOMER_AGENT", "COMPLETE_CUSTOMER_NODE",
                    "PLAN_CREATOR_AGENT", "COMPLETE_PLAN_NODE", "END"
                ]

                if nextAgent and nextAgent in validAgents:
                    self.logger.info(f"✅ 路由决策: {nextAgent} (置信度: {routingConfidence:.2f})")
                    self.logger.debug(f"决策理由: {routingReasoning}")
                    return nextAgent

                # 默认进入意图识别
                self.logger.warning(f"❌ 路由决策无效: {nextAgent}, 默认进入意图识别")
                return "INTENTION_AGENT"

            except Exception as e:
                self.logger.error(f"❌ 路由决策失败: {str(e)}")
                return "INTENTION_AGENT"

        workflow.add_conditional_edges(
            "ROUTER_AGENT",
            routerNextSelector,
            {
                "INTENTION_AGENT": "INTENTION_AGENT",
                "COMPLETE_INTENT_NODE": "COMPLETE_INTENT_NODE",
                "TAG_CUSTOMER_AGENT": "TAG_CUSTOMER_AGENT",
                "COMPLETE_CUSTOMER_NODE": "COMPLETE_CUSTOMER_NODE",
                "PLAN_CREATOR_AGENT": "PLAN_CREATOR_AGENT",
                "COMPLETE_PLAN_NODE": "COMPLETE_PLAN_NODE",
                "END": "END"
            }
        )



    def _configureIntentionEdges(self, workflow: StateGraph):
        """配置意图Agent的条件边"""
        def intentNextSelector(state: MarketingState) -> str:
            """意图Agent的下一步选择器"""
            if state.get("needCompletion"):
                return "COMPLETE_INTENT_NODE"

            # 标记意图信息为已完成
            intentInfo = state.get("intentInfo", {}) or {}
            intentInfo["completed"] = True
            state.update({"intentInfo": intentInfo})
            return "ROUTER_AGENT"

        workflow.add_conditional_edges(
            "INTENTION_AGENT",
            intentNextSelector,
            {
                "COMPLETE_INTENT_NODE": "COMPLETE_INTENT_NODE",
                "ROUTER_AGENT": "ROUTER_AGENT"
            }
        )

    def _configureCustomerEdges(self, workflow: StateGraph):
        """配置客群Agent的条件边"""
        def customerNextSelector(state):
            """客群Agent的下一步选择器"""
            customerTags = state.get("customerTags", {})
            if not customerTags.get("tags"):
                return "COMPLETE_CUSTOMER_NODE"

            # 标记客群信息为已完成
            customerTags["completed"] = True
            state["customerTags"] = customerTags
            return "ROUTER_AGENT"

        workflow.add_conditional_edges(
            "TAG_CUSTOMER_AGENT",
            customerNextSelector,
            {
                "COMPLETE_CUSTOMER_NODE": "COMPLETE_CUSTOMER_NODE",
                "ROUTER_AGENT": "ROUTER_AGENT"
            }
        )
    def _configurePlanEdges(self, workflow: StateGraph):
        """配置方案Agent的条件边"""
        def planNextSelector(state):
            """方案Agent的下一步选择器"""
            planDraft = state.get("planDraft", {})
            if not planDraft.get("content"):
                return "COMPLETE_PLAN_NODE"

            # 方案生成完成，标记状态
            planDraft.update({
                "completed": True,
                "needsConfirmation": True,
                "status": "awaiting_confirmation"
            })
            state["planDraft"] = planDraft

            # 设置工作流状态
            state.update({
                "workflowStatus": "plan_generated",
                "nextAction": "show_plan_to_user"
            })

            return "END"

        workflow.add_conditional_edges(
            "PLAN_CREATOR_AGENT",
            planNextSelector,
            {
                "COMPLETE_PLAN_NODE": "COMPLETE_PLAN_NODE",
                "END": "END"
            }
        )

    def _configureCompletionEdges(self, workflow: StateGraph):
        """配置补全节点的直接边"""
        workflow.add_edge("COMPLETE_INTENT_NODE", "INTENTION_AGENT")
        workflow.add_edge("COMPLETE_CUSTOMER_NODE", "TAG_CUSTOMER_AGENT")
        workflow.add_edge("COMPLETE_PLAN_NODE", "PLAN_CREATOR_AGENT")

    # ========== 补全节点方法 ==========

    def _completeIntentNode(self, state: MarketingState) -> Any:
        """意图补全节点 - 中断工作流等待用户输入"""
        guidancePrompts = state.get("guidancePrompts", {})
        fieldSuggestions = state.get("fieldSuggestions", {})
        followupQuestions = state.get("followupQuestions", {})
        missingFields = state.get("missingFields", {})

        interruptMessage = {
            "type": "intent_completion",
            "guidancePrompts": guidancePrompts,
            "fieldSuggestions": fieldSuggestions,
            "followupQuestions": followupQuestions,
            "missingFields": missingFields.get("missingCoreFields", []) if missingFields else [],
            "stateContext": state
        }

        return interrupt(interruptMessage)

    def _completeCustomerNode(self, state: MarketingState) -> Dict[str, Any]:
        """客群补全节点 - 收集目标客群信息"""
        customerTags = state.get("customerTags", {})

        if not customerTags.get("tags"):
            # 客群标签建议选项
            customerSuggestions = [
                {"value": "新注册用户", "description": "刚注册的新用户"},
                {"value": "活跃用户", "description": "经常使用产品的用户"},
                {"value": "高价值客户", "description": "消费金额较高的用户"},
                {"value": "流失用户", "description": "长时间未使用的用户"},
                {"value": "年轻用户", "description": "18-30岁年龄段用户"},
                {"value": "企业客户", "description": "B2B企业用户"}
            ]

            userInput = interrupt({
                "type": "user_input_needed",
                "nodeId": "COMPLETE_CUSTOMER_NODE",
                "prompt": "请补充目标客群信息，以便为您制定精准的营销方案",
                "description": "客群标签帮助我们了解您的目标用户特征，制定更有效的营销策略",
                "missingFields": [{
                    "field": "tags",
                    "label": "客群标签",
                    "required": True,
                    "type": "multiselect",
                    "placeholder": "请选择或输入客群标签"
                }],
                "suggestions": customerSuggestions,
                "currentInfo": customerTags,
                "canSkip": False,
                "skipReason": "客群信息是制定营销方案的必要条件"
            })

            # 处理用户输入
            customerTags = self._processCustomerInput(userInput, customerTags)

        return {"customerTags": customerTags}

    def _processCustomerInput(self, userInput, customerTags: Dict[str, Any]) -> Dict[str, Any]:
        """处理客群输入数据"""
        if isinstance(userInput, dict):
            if "tags" in userInput:
                customerTags["tags"] = userInput["tags"]
            customerTags.update({k: v for k, v in userInput.items() if k != "tags"})
        elif isinstance(userInput, str):
            customerTags["tags"] = [tag.strip() for tag in userInput.split(",")]
        elif isinstance(userInput, list):
            customerTags["tags"] = userInput

        return customerTags

    def _completePlanNode(self, state: MarketingState) -> Dict[str, Any]:
        """方案补全节点 - 收集营销方案详细内容"""
        planDraft = state.get("planDraft", {})

        if not planDraft.get("content"):
            # 方案内容建议选项
            planSuggestions = [
                {"value": "限时折扣活动", "description": "设置时间限制的优惠活动"},
                {"value": "新用户专享", "description": "针对新用户的专属优惠"},
                {"value": "会员积分兑换", "description": "使用积分兑换优惠或礼品"},
                {"value": "社交分享奖励", "description": "分享获得额外优惠"}
            ]

            userInput = interrupt({
                "type": "user_input_needed",
                "nodeId": "COMPLETE_PLAN_NODE",
                "prompt": "请补充营销方案的具体内容",
                "description": "详细的方案内容有助于制定更完整的营销策略",
                "missingFields": [{
                    "field": "content",
                    "label": "方案内容",
                    "required": True,
                    "type": "textarea",
                    "placeholder": "请描述活动主题、执行步骤、预期效果等"
                }],
                "suggestions": planSuggestions,
                "currentInfo": planDraft,
                "canSkip": False,
                "skipReason": "方案内容是营销活动的核心要素"
            })

            # 处理用户输入
            planDraft = self._processPlanInput(userInput, planDraft)

        return {"planDraft": planDraft}

    def _processPlanInput(self, userInput, planDraft: Dict[str, Any]) -> Dict[str, Any]:
        """处理方案输入数据"""
        if isinstance(userInput, dict):
            planDraft.update(userInput)
        elif isinstance(userInput, str):
            planDraft["content"] = userInput

        return planDraft

    async def executeStreamWorkflow(self, context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        流式执行营销工作流

        Args:
            context: 工作流执行上下文，包含所有必要的配置和状态

        Yields:
            Dict[str, Any]: 包含事件类型和数据的流式事件
        """
        # 初始化流式执行所需的额外字段
        self.initializeStreamExecution(context)

        try:
            # 发送工作流开始事件，通知前端开始处理
            yield self.createWorkflowStartEvent(context)

            # 执行核心流式处理逻辑，实时推送各种事件
            async for event in self.processWorkflowStreamSimplified(context):
                yield event

            # 检查工作流完成状态，处理正常结束或中断情况
            for event in self.handleWorkflowCompletion(context):
                yield event

        except Exception as e:
            self.logger.error(f"流式工作流执行失败: {str(e)}")
            yield {
                "event": "error",
                "messageId": context.messageId,
                "content": f"工作流执行失败: {str(e)}",
                "data": {"error": str(e)}
            }

    # 注意：executeStreamWorkflowSimplified 方法功能已合并到 executeStreamWorkflow

    async def processWorkflowStreamSimplified(self, context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        简化版工作流流式处理

        只负责调用LangGraph并返回原始事件，不进行事件转换

        Args:
            context: 流式执行上下文

        Yields:
            Dict[str, Any]: 原始流式事件
        """
        for streamEvent in self.app.stream(
            context.currentState,
            config=context.config,
            stream_mode=["messages", "updates", "custom"]
        ):
            # 1. 处理LangGraph的标准流式事件格式
            if isinstance(streamEvent, tuple):
                mode, data = streamEvent
                yield {
                    "mode": mode,
                    "data": data,
                    "messageId": context.messageId,
                    "threadId": context.threadId
                }
            else:
                # 3. 兼容处理其他格式的事件
                if isinstance(streamEvent, dict):
                    for nodeName, nodeOutput in streamEvent.items():
                        yield {
                            "event": "nodeFinished",
                            "messageId": context.messageId,
                            "data": {
                                "nodeId": nodeName,
                                "nodeResult": nodeOutput
                            }
                        }

    async def resumeInterruptedWorkflowSimplified(self, context: WorkflowContext) -> AsyncGenerator[Dict[str, Any], None]:
        """
        简化版中断恢复工作流

        专注于工作流恢复，事件处理交给服务层

        Args:
            context: 工作流执行上下文

        Yields:
            Dict[str, Any]: 原始流式事件
        """
        try:
            # 使用Command原语恢复工作流
            async for streamEvent in self.app.astream(
                Command(resume=context.userInput),
                config=context.config
            ):
                if isinstance(streamEvent, tuple):
                    mode, data = streamEvent
                    yield {
                        "mode": mode,
                        "data": data,
                        "messageId": context.messageId,
                        "threadId": context.threadId
                    }

            # 发送工作流完成事件
            yield {
                "event": "workflowEnd",
                "messageId": context.messageId,
                "content": "工作流恢复完成",
                "data": {
                    "status": "succeeded",
                    "totalSteps": 5,
                    "completedSteps": 5
                }
            }

        except Exception as e:
            self.logger.error(f"简化版中断恢复失败: {str(e)}")
            yield {
                "event": "error",
                "messageId": context.messageId,
                "content": f"中断恢复失败: {str(e)}",
                "data": {"error": str(e)}
            }

        except GeneratorExit:
            # 客户端主动断开连接时的优雅处理
            self.logger.info("流式工作流被客户端中断")
            return
        except Exception as e:
            # 统一处理所有异常情况，包括中断和错误
            for event in self.handleStreamError(e, executionContext):
                yield event
        finally:
            # 确保资源得到正确清理，避免内存泄漏
            self.cleanupStreamExecution()

    def initializeStreamExecution(self, context: WorkflowContext) -> None:
        """
        初始化流式执行上下文

        在传入的上下文基础上，设置流式执行所需的额外字段

        Args:
            context: 工作流执行上下文，将在此基础上添加流式执行所需的字段
        """
        self.logger.info(f"开始流式执行营销工作流，用户输入: {context.userInput[:50]}...")

        # 准备工作流初始状态，支持会话恢复
        try:
            existingState = self.app.get_state(context.config)
            if existingState and existingState.values:
                # 恢复现有会话状态，更新用户输入
                currentState = existingState.values
                currentState["userInput"] = context.userInput
            else:
                # 创建新的工作流状态
                currentState = MarketingState(userInput=context.userInput, planRetryCount=0)
        except:
            # 异常情况下创建默认状态
            currentState = MarketingState(userInput=context.userInput, planRetryCount=0)

        # 设置流式执行所需的字段
        context.currentState = currentState
        context.startTime = int(datetime.now().timestamp())

        # 设置当前消息标识符，供节点事件使用
        self.currentMessageId = context.messageId

        # 初始化事件收集器和回调管理器
        self.currentStreamEvents = []
        self.activeCallbacks = {}

    def createWorkflowStartEvent(self, context: WorkflowContext) -> Dict[str, Any]:
        """
        创建工作流开始事件

        生成标准格式的工作流开始事件，通知前端开始处理流程

        Args:
            context: 流式执行上下文，包含消息ID等信息

        Returns:
            Dict[str, Any]: 格式化的开始事件数据
        """
        return StreamEventBuilder.createWorkflowStartedEvent(
            messageId=context.messageId,
            conversationId="",  # 会话ID将在上层API中填充
            totalSteps=5  # 营销工作流的总步数
        )

    def handleWorkflowCompletion(self, context: WorkflowContext) -> Generator[Dict[str, Any], None, None]:
        """
        处理工作流完成状态

        检查工作流是否正常完成或被中断，并生成相应的完成事件

        Args:
            context: 流式执行上下文

        Yields:
            Dict[str, Any]: 工作流完成或中断事件
        """
        finalState = self.app.get_state(context.config)
        if finalState and finalState.next:
            # 工作流被中断，需要用户补充信息
            yield StreamEventBuilder.createWorkflowEndEvent(
                messageId=context.messageId,
                conversationId="",  # 会话ID将在上层填充
                status=WorkflowStatus.INTERRUPTED,
                totalSteps=5,
                completedSteps=3,  # 估算的已完成步数
                elapsedTime=int(datetime.now().timestamp()) - context.startTime,
                error="需要用户补充信息"
            )
        # 注意：工作流正常完成时不在这里发送事件，由上层API统一处理

    def handleStreamError(self, error: Exception, context: WorkflowContext) -> Generator[Dict[str, Any], None, None]:
        """
        统一处理流式执行过程中的错误

        区分中断异常和真正的错误，生成相应的事件通知前端

        Args:
            error: 捕获到的异常对象
            context: 流式执行上下文

        Yields:
            Dict[str, Any]: 错误或中断事件
        """
        errorInfo = f"streamExecutionError: {type(error).__name__} - {str(error)}"
        self.logger.error(errorInfo)

        if self.isInterruptError(error):
            # 处理工作流中断异常（这是正常的业务流程）
            try:
                finalState = self.app.get_state(context.config)
                if finalState and finalState.next:
                    # 工作流确实被中断，需要用户输入
                    yield StreamEventBuilder.createInterruptEvent(
                        messageId=context.messageId,
                        conversationId="",
                        content="工作流已中断，等待用户补充信息",
                        data={
                            "threadId": context.threadId,
                            "nextNode": finalState.next[0] if finalState.next else None,
                            "status": "interrupted",
                            "statusText": "需要更多信息才能继续",
                            "showContinue": True,
                            "canResume": True
                        }
                    )
                else:
                    # 虽然抛出中断异常，但工作流实际已完成
                    yield StreamEventBuilder.createWorkflowEndEvent(
                        messageId=context.messageId,
                        conversationId="",
                        status="succeeded",
                        totalSteps=5,
                        completedSteps=5,
                        elapsedTime=int(datetime.now().timestamp()) - context.startTime
                    )
            except Exception as stateError:
                # 获取状态时发生异常，创建错误事件
                self.logger.error(f"获取状态失败: {str(stateError)}")
                yield StreamEventBuilder.createWorkflowEndEvent(
                    messageId=context.messageId,
                    conversationId="",
                    status=WorkflowStatus.INTERRUPTED,
                    totalSteps=5,
                    completedSteps=2,
                    elapsedTime=int(datetime.now().timestamp()) - context.startTime,
                    error="获取状态失败"
                )
        else:
            # 处理真正的执行错误
            yield StreamEventBuilder.createErrorEvent(
                messageId=context.messageId,
                conversationId="",
                errorCode="STREAM_EXECUTION_ERROR",
                errorMessage=f"流式执行错误: {str(error)}"
            )

    def cleanupStreamExecution(self):
        """
        清理流式执行相关的资源

        确保在流式执行结束后正确清理内存中的临时数据，
        避免内存泄漏和状态污染
        """
        if hasattr(self, 'activeCallbacks'):
            self.activeCallbacks.clear()
        if hasattr(self, 'currentStreamEvents'):
            self.currentStreamEvents.clear()


    def isInterruptError(self, error: Exception) -> bool:
        """
        检查异常是否为工作流中断相关的错误

        工作流中断是正常的业务流程（需要用户补充信息），
        不应该被当作真正的错误处理

        Args:
            error: 要检查的异常对象

        Returns:
            bool: True表示是中断异常，False表示是真正的错误
        """
        errorStr = str(error).lower()
        errorType = str(type(error).__name__).lower()

        # 定义各种中断相关的错误模式
        interruptPatterns = [
            "interrupt",
            "interrupted",
            "graphinterrupt",
            "nodeinterrupt",
            "execution interrupted",
            "workflow interrupted"
        ]

        # 检查错误信息或类型名是否包含中断模式
        for pattern in interruptPatterns:
            if pattern in errorStr or pattern in errorType:
                return True

        return False

    async def isInterruptedWorkflow(self, threadId: str) -> bool:
        """检查工作流是否处于中断状态"""
        if not threadId:
            return False

        try:
            config = {"configurable": {"thread_id": threadId}}
            state = self.app.get_state(config)
            # 如果有next节点，说明工作流被中断了
            return state and hasattr(state, 'next') and state.next
        except Exception as e:
            self.logger.debug(f"检查中断状态失败: {e}")
            return False

    async def resumeInterruptedWorkflow(self, userInput: str, threadId: str, messageId: str) -> AsyncGenerator[Dict[str, Any], None]:
        """恢复被中断的工作流"""
        try:
            from langgraph.types import Command

            config = {"configurable": {"thread_id": threadId}}
            self.logger.info(f"恢复中断的工作流: {threadId}")

            # 使用Command原语恢复工作流
            async for streamEvent in self.app.astream(
                Command(resume=userInput),
                config=config
            ):
                if isinstance(streamEvent, tuple):
                    mode, data = streamEvent

                    if mode == "messages":
                        # 处理LLM token流
                        event = self.processStreamEvent(data, "message", messageId, "")
                        if event:
                            yield event

                    elif mode == "updates":
                        # 处理节点更新
                        events = self.handleNodeUpdateEvents(data, messageId, "")
                        for event in events:
                            yield event

                    elif mode == "custom":
                        # 处理自定义事件（包括新的中断）
                        event = self.processStreamEvent(data, "custom", messageId, "")
                        if event:
                            yield event

            # 发送工作流完成事件
            yield StreamEventBuilder.createWorkflowEndEvent(
                messageId=messageId,
                conversationId="",
                status="succeeded",
                totalSteps=5,
                completedSteps=5,
                elapsedTime=0.0
            )

        except Exception as e:
            self.logger.error(f"恢复工作流失败: {str(e)}")
            yield {
                "event": "error",
                "messageId": messageId,
                "conversationId": "",
                "content": f"恢复工作流失败: {str(e)}",
                "data": {"errorCode": "RESUME_FAILED"},
                "createdAt": int(datetime.now().timestamp() * 1000)
            }

    def createNodeWrapper(self, nodeId: str, originalFunc, enableStreaming: bool = True, sendFinishEvent: bool = False):
        """
        统一的节点包装器，支持流式和非流式模式

        Args:
            nodeId: 节点ID
            originalFunc: 原始节点执行函数
            enableStreaming: 是否启用流式处理
            sendFinishEvent: 是否发送完成事件

        Returns:
            包装后的节点函数
        """
        def wrappedNodeFunc(state):
            """包装后的节点执行函数"""
            # 设置当前执行节点状态
            self.currentExecutingNode = nodeId
            self.logger.debug(f"节点开始执行: {nodeId}")

            # 发送节点开始事件
            if hasattr(self, 'currentStreamEvents') and self.currentStreamEvents is not None:
                self.sendNodeStartEvent(nodeId, state)

            try:
                # 执行原始节点函数
                result = originalFunc(state)

                # 发送节点完成事件
                if sendFinishEvent and hasattr(self, 'currentStreamEvents') and self.currentStreamEvents is not None:
                    self.sendNodeFinishEvent(nodeId, result)

                return result
            except Exception as e:
                self.logger.error(f"节点 {nodeId} 执行失败: {str(e)}")
                raise
            finally:
                # 清理状态
                self.currentExecutingNode = None
                self.logger.debug(f"节点执行完成: {nodeId}")

        return wrappedNodeFunc

    def sendNodeStartEvent(self, nodeId: str, state: Dict[str, Any]):
        """发送节点开始事件"""
        try:
            messageId = getattr(self, 'currentMessageId', f"msg_{str(uuid.uuid4())[:8]}")

            nodeStartedEvent = StreamEventBuilder.createNodeStartedEvent(
                messageId=messageId,
                conversationId="",  # 将在上层添加
                nodeId=nodeId,
                stepNumber=getNodeIndex(nodeId),
                totalSteps=5
            )

            self.currentStreamEvents.append(nodeStartedEvent)
            self.logger.debug(f"发送节点开始事件: {nodeId}")
        except Exception as e:
            self.logger.warning(f"发送节点开始事件失败: {e}")

    def sendNodeFinishEvent(self, nodeId: str, result: Dict[str, Any]):
        """发送节点完成事件"""
        try:
            messageId = getattr(self, 'currentMessageId', f"msg_{str(uuid.uuid4())[:8]}")

            nodeFinishedEvent = StreamEventBuilder.createNodeEndEvent(
                messageId=messageId,
                conversationId="",
                nodeId=nodeId,
                nodeName=getNodeDisplayName(nodeId),
                status="succeeded",
                stepNumber=getNodeIndex(nodeId),
                outputs=result
            )

            self.currentStreamEvents.append(nodeFinishedEvent)
            self.logger.debug(f"发送节点完成事件: {nodeId}")
        except Exception as e:
            self.logger.warning(f"发送节点完成事件失败: {e}")

    def getMermaidDiagram(self) -> str:
        """
        获取工作流的Mermaid图

        Returns:
            str: Mermaid图代码
        """
        try:
            return self.workflow.compile().get_graph().draw_mermaid()
        except Exception as e:
            self.logger.error(f"生成Mermaid图失败: {str(e)}")
            return "graph TD\n    A[工作流图生成失败]"


def createWorkflow() -> MarketingWorkflow:
    """
    创建工作流实例

    Returns:
        MarketingWorkflow: 工作流实例
    """
    return MarketingWorkflow()
