"""
数据模型定义，使用TypedDict进行类型标注和状态管理

主要包含:
1. 意图识别相关类型 (IntentInfo)
2. 客群标签相关类型 (CustomerTags)
3. 历史方案检索相关类型 (RagContext)
4. 营销方案相关类型 (PlanDraft)
5. 缺失字段相关类型 (MissingFields)
6. 全局状态类型 (MarketingState)

所有类型都使用TypedDict实现，支持:
- 类型提示和检查
- 可选字段 (通过total=False)
- IDE智能提示
- 运行时类型验证
"""
from langgraph.graph import MessagesState
from typing import TypedDict, Optional, List, Dict, Any
from datetime import datetime

# 1. 意图识别结果
class IntentInfo(TypedDict, total=False):
    """意图识别结果类型
    
    用于存储从用户输入中提取的营销意图信息。
    total=False 表示所有字段都是可选的。
    
    字段说明:
    - bankCode: 银行编号，用于标识具体银行
    - bankName: 银行名称，用于显示
    - goal: 营销活动目标，例如"提升存款规模"
    - activityType: 活动类型，例如"存款促销"、"信用卡推广"
    - roi: 预期投入产出比，可选
    - audience: 目标客群描述，可选
    - activityTime: 活动时间范围
    - activityBudget: 活动预算范围
    - extra: 其他补充信息，键值对格式
    - completed: 标记是否完成意图提取
    """
    bankCode: str  # 银行号
    bankName: str  # 银行名称
    goal: str  # 活动目标
    activityType: str  # 活动类型
    roi: Optional[str]  # 投入/产出ROI
    audience: Optional[str]  # 目标客群
    activityTime: str  # 活动时间
    activityBudget: str  # 活动预算
    extra: Dict[str, Any]  # 其他相关要求
    completed: bool  # 是否完成

# 2. 客群标签信息
class CustomerTags(TypedDict, total=False):
    """客群标签信息类型
    
    用于存储客群画像和标签信息。
    total=False 表示所有字段都是可选的。
    
    字段说明:
    - tags: 客群标签列表，例如["高净值", "稳健型"]
    - characteristics: 客群特征列表，例如["偏好理财产品", "风险承受能力较高"]
    - preferences: 偏好信息，包含产品偏好、渠道偏好等
    - behaviors: 行为特征列表，例如["经常使用手机银行", "定期购买理财产品"]
    - segments: 客群细分类型列表，例如["私行客户", "理财客户"]
    - completed: 标记是否完成客群分析
    """
    tags: List[str]  # 标签列表
    characteristics: List[str]  # 特征列表
    preferences: Dict[str, Any]  # 偏好信息
    behaviors: List[str]  # 行为特征
    segments: List[str]  # 细分类型
    completed: bool  # 是否完成

# 3. 历史方案检索上下文
class RagContext(TypedDict, total=False):
    """历史方案检索上下文类型
    
    用于存储RAG(检索增强生成)的相关上下文信息。
    total=False 表示所有字段都是可选的。
    
    字段说明:
    - similarCases: 相似历史案例列表，每个案例包含详细信息
    - relevantPolicies: 相关政策法规列表
    - industryInsights: 行业洞察列表
    - bestPractices: 最佳实践列表，每个实践包含详细信息
    """
    similarCases: List[Dict[str, Any]]  # 相似案例
    relevantPolicies: List[str]  # 相关政策
    industryInsights: List[str]  # 行业洞察
    bestPractices: List[Dict[str, Any]]  # 最佳实践

# 4. 营销方案草稿
class PlanDraft(TypedDict, total=False):
    """营销方案草稿类型
    
    用于存储营销方案的各个组成部分。
    total=False 表示所有字段都是可选的。
    
    字段说明:
    - title: 方案标题
    - outline: 方案大纲要点列表
    - content: 完整的方案内容
    - channels: 投放渠道列表
    - budget: 预算分配详情
    - timeline: 执行时间线详情
    - metrics: 效果评估指标列表
    - risks: 风险点列表
    - completed: 标记是否完成方案制定
    """
    title: str  # 方案标题
    outline: List[str]  # 方案大纲
    content: str  # 方案内容
    channels: List[str]  # 投放渠道
    budget: Dict[str, Any]  # 预算分配
    timeline: Dict[str, Any]  # 执行时间线
    metrics: List[str]  # 效果指标
    risks: List[str]  # 风险点
    completed: bool  # 是否完成

# 5. 缺失字段信息
class MissingFields(TypedDict, total=False):
    """缺失字段信息类型
    
    用于跟踪和管理需要补充的字段信息。
    total=False 表示所有字段都是可选的。
    
    字段说明:
    - missingCoreFields: 必须补充的核心字段列表
    - missingSecondaryFields: 可选补充的次要字段列表
    - fieldSuggestions: 每个字段的建议值列表
    - fieldDescriptions: 每个字段的描述说明
    """
    missingCoreFields: List[str]  # 核心缺失字段
    missingSecondaryFields: List[str]  # 次要缺失字段
    fieldSuggestions: Dict[str, List[str]]  # 字段建议值
    fieldDescriptions: Dict[str, str]  # 字段描述

# 6. 全局状态
class MarketingState(MessagesState):
    """营销工作流状态类型定义
    
    用于管理整个营销工作流的状态信息。
    total=False 表示所有字段都是可选的。
    
    状态分类:
    1. 用户和会话信息 - 用于身份识别和会话管理
    2. 工作流状态 - 追踪当前执行阶段
    3. 意图识别状态 - 存储意图分析结果
    4. 客群分析状态 - 存储客群画像结果
    5. 知识检索状态 - 存储RAG检索结果
    6. 营销方案状态 - 存储方案内容
    7. 辅助信息 - 存储交互提示信息
    8. 消息历史 - 存储对话历史记录
    
    使用说明:
    1. 所有字段都是可选的，使用时需要通过.get()方法安全访问
    2. 更新状态时使用字典update方法
    3. 可以直接序列化为JSON
    4. 支持类型检查和IDE提示
    """
    # 用户和会话信息
    userId: str  # 用户唯一标识符
    sessionId: str  # 会话唯一标识符
    userProfile: Optional[Dict[str, Any]]  # 用户画像信息
    sessionStartTime: datetime  # 会话开始时间
    # 当前用户输入（用于处理）
    userInput: Optional[str] = None

    # 工作流状态
    workflowId: str  # 工作流实例唯一标识符
    currentStep: str  # 当前工作流步骤
    
    # 意图识别状态
    intentInfo: Optional[IntentInfo]  # 意图识别结果
    needCompletion: bool  # 是否需要补全意图信息
    missingFields: Optional[MissingFields]  # 缺失字段信息
    
    # 客群分析状态
    customerTags: Optional[CustomerTags]  # 客群标签信息
    
    # 知识检索状态
    ragContext: Optional[RagContext]  # 历史方案检索上下文
    
    # 营销方案状态
    planDraft: Optional[PlanDraft]  # 营销方案草稿
    
    # 辅助信息
    guidancePrompts: Dict[str, Any]  # 引导话术
    fieldSuggestions: Dict[str, Any]  # 字段建议
    followupQuestions: Dict[str, Any]  # 追问问题

    def __init__(self, **kwargs):
        """初始化状态对象"""
        super().__init__()
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "MarketingState":
        """从字典创建状态对象
        
        Args:
            data: 状态字典
        
        Returns:
            MarketingState: 状态对象
        """
        # 处理特殊字段的类型转换
        if "messages" in data:
            messages = data.pop("messages")
            state = cls(**data)
            state.messages = messages
            return state
        return cls(**data)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典
        
        Returns:
            Dict[str, Any]: 状态字典
        """
        result = {}
        for key, value in self.__dict__.items():
            if key == "messages":
                continue  # 跳过messages字段,由MessagesState处理
            result[key] = value
        return result

    def get(self, key: str, default: Any = None) -> Any:
        """获取状态值"""
        return getattr(self, key, default)
    
    def update(self, data: Dict[str, Any]) -> None:
        """更新状态"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def updateUserProfile(self, updates: Dict[str, Any], merge: bool = True) -> None:
        """更新用户画像信息"""
        if merge and self.userProfile:
            self.userProfile.update(updates)
        else:
            self.userProfile = updates
    
    def updateIntent(self, intentInfo: IntentInfo, needCompletion: bool) -> None:
        """更新意图信息"""
        self.intentInfo = intentInfo
        self.needCompletion = needCompletion
        self.currentStep = "intentionCompleted" if not needCompletion else "intentionPending"
    
    def hasMissingFields(self) -> bool:
        """检查是否有缺失字段"""
        if not self.missingFields:
            return False
        return bool(self.missingFields.get("missingCoreFields", []))
    
    def getMissingFieldsSummary(self) -> Dict[str, Any]:
        """获取缺失字段摘要"""
        if not self.missingFields:
            return {"hasMissing": False, "core": [], "secondary": []}
        
        return {
            "hasMissing": True,
            "core": self.missingFields.get("missingCoreFields", []),
            "secondary": self.missingFields.get("missingSecondaryFields", []),
            "suggestions": self.missingFields.get("fieldSuggestions", {}),
            "descriptions": self.missingFields.get("fieldDescriptions", {})
        }



class WorkflowContext:
    """工作流执行上下文"""

    def __init__(self,
                 workflowInput: Dict[str, Any],
                 config: Dict[str, Any],
                 conversationId: str,
                 threadId: str,
                 sessionId: str,
                 messageId: str,
                 userMemory: Dict[str, Any],
                 currentState: Any = None,
                 startTime: int = None):
        self.workflowInput = workflowInput
        self.config = config
        self.conversationId = conversationId
        self.threadId = threadId
        self.sessionId = sessionId
        self.messageId = messageId
        self.userMemory = userMemory
        # 新增字段，兼容 StreamExecutionContext 的功能
        self.currentState = currentState
        self.startTime = startTime or int(datetime.now().timestamp())

    @property
    def userInput(self) -> str:
        """获取用户输入，兼容 StreamExecutionContext"""
        return self.workflowInput.get("userInput", "")