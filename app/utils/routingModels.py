"""
路由相关的数据模型定义
设计思路：使用Pydantic确保数据结构的类型安全和验证，为后续LLM增强提供标准化接口
"""

from typing import Dict, Any, List, Optional, Literal
from pydantic import BaseModel, Field
from datetime import datetime


class UserIntentAnalysis(BaseModel):
    """
    用户意图分析结果
    设计思路：结构化存储意图识别的所有相关信息，便于后续决策使用
    """
    intentType: Literal["consultation", "planGeneration", "unclear"] = Field(
        description="用户意图类型：咨询探索/方案生成/不明确"
    )
    confidence: float = Field(
        description="意图识别置信度",
        ge=0.0, le=1.0
    )
    matchedKeywords: List[str] = Field(
        description="匹配到的关键词列表",
        default_factory=list
    )
    reasoning: str = Field(
        description="意图判断的详细推理过程"
    )
    inputComplexity: Literal["simple", "medium", "complex"] = Field(
        description="用户输入的复杂度评估"
    )
    analysisMethod: Literal["rule_based", "llm_enhanced", "hybrid"] = Field(
        description="分析方法类型",
        default="rule_based"
    )
    analysisTimestamp: datetime = Field(
        description="分析时间戳",
        default_factory=datetime.now
    )


class MarketingStateAnalysis(BaseModel):
    """
    营销工作流状态分析结果
    设计思路：全面评估当前工作流的完整性和进度
    """
    moduleCompleteness: Dict[str, bool] = Field(
        description="各模块的完整性状态映射"
    )
    missingCriticalFields: List[str] = Field(
        description="缺失的关键字段列表",
        default_factory=list
    )
    workflowStage: Literal[
        "initialization", "intentAnalysis", "dataCollection", 
        "customerAnalysis", "planCreation", "finalization"
    ] = Field(
        description="当前工作流所处阶段"
    )
    informationSufficiency: float = Field(
        description="信息充分性评分",
        ge=0.0, le=1.0
    )
    dataSourceAvailability: Dict[str, bool] = Field(
        description="各数据源的可用性状态",
        default_factory=dict
    )
    qualityScore: float = Field(
        description="整体数据质量评分",
        ge=0.0, le=1.0
    )
    analysisMethod: Literal["rule_based", "llm_enhanced", "hybrid"] = Field(
        description="分析方法类型",
        default="rule_based"
    )


class RoutingDecision(BaseModel):
    """
    路由决策结果
    设计思路：包含完整的决策信息，支持决策追踪和调试
    """
    nextAgent: Literal[
        "INTENTION_AGENT", "COMPLETE_INTENT_NODE",
        "TAG_CUSTOMER_AGENT", "COMPLETE_CUSTOMER_NODE", "PLAN_CREATOR_AGENT",
        "COMPLETE_PLAN_NODE", "END", "ERROR_HANDLER"
    ] = Field(
        description="下一个要执行的Agent节点"
    )
    intentType: str = Field(
        description="识别的用户意图类型"
    )
    routingStrategy: Literal["dataDriven", "userInteraction", "hybrid"] = Field(
        description="采用的路由策略"
    )
    reasoning: str = Field(
        description="路由决策的详细推理过程"
    )
    confidence: float = Field(
        description="决策置信度",
        ge=0.0, le=1.0
    )
    expectedOutcome: str = Field(
        description="预期的执行结果"
    )
    fallbackOptions: List[str] = Field(
        description="备选执行方案",
        default_factory=list
    )
    executionPriority: Literal["high", "medium", "low"] = Field(
        description="执行优先级",
        default="medium"
    )
    riskAssessment: str = Field(
        description="风险评估结果",
        default=""
    )
    decisionMethod: Literal["rule_based", "llm_enhanced", "hybrid"] = Field(
        description="决策方法类型",
        default="rule_based"
    )
    decisionTimestamp: datetime = Field(
        description="决策时间戳",
        default_factory=datetime.now
    )


class DataSourceQueryPlan(BaseModel):
    """
    数据源查询计划
    设计思路：结构化规划数据查询策略，确保高效获取所需信息
    """
    historicalCampaigns: Dict[str, Any] = Field(
        description="历史营销活动查询配置",
        default_factory=dict
    )
    customerInsights: Dict[str, Any] = Field(
        description="客户洞察数据查询配置",
        default_factory=dict
    )
    marketBenchmarks: Dict[str, Any] = Field(
        description="市场基准数据查询配置",
        default_factory=dict
    )
    bestPractices: Dict[str, Any] = Field(
        description="最佳实践知识查询配置",
        default_factory=dict
    )
    priorityOrder: List[str] = Field(
        description="查询执行的优先级顺序",
        default_factory=list
    )
    estimatedDuration: int = Field(
        description="预估查询总耗时（秒）",
        default=30
    )
