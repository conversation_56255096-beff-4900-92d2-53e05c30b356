"""
通用工具类 - 整合所有重复的工具方法

这个文件整合了项目中重复出现的工具函数，避免代码重复。
包括：节点相关、内容过滤、响应格式化、错误处理等通用功能。

Author: Marketing AI Team
Version: 1.0.0
Date: 2025-07-31
"""

import functools
import uuid
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger


class NodeUtils:
    """节点相关工具类"""
    
    # 节点显示名称映射
    NODE_DISPLAY_NAMES = {
        "ROUTER_AGENT": "智能路由",
        "INTENTION_AGENT": "意图识别", 
        "TAG_CUSTOMER_AGENT": "客群分析",
        "PLAN_CREATOR_AGENT": "方案生成",
        "COMPLETE_INTENT_NODE": "意图补全",
        "COMPLETE_CUSTOMER_NODE": "客群补全", 
        "COMPLETE_PLAN_NODE": "方案补全",
        "END": "流程结束"
    }
    
    # 节点执行顺序
    NODE_ORDER = {
        "ROUTER_AGENT": 1,
        "INTENTION_AGENT": 2,
        "TAG_CUSTOMER_AGENT": 3,
        "PLAN_CREATOR_AGENT": 4,
        "COMPLETE_INTENT_NODE": 2,
        "COMPLETE_CUSTOMER_NODE": 3,
        "COMPLETE_PLAN_NODE": 4,
        "END": 5
    }
    
    # 友好消息映射
    FRIENDLY_MESSAGES = {
        'ROUTER_AGENT': {
            'start': '正在分析您的营销需求...',
            'complete': '需求分析完成'
        },
        'INTENTION_AGENT': {
            'start': '正在深入理解您的营销目标...',
            'complete': '营销目标分析完成'
        },
        'TAG_CUSTOMER_AGENT': {
            'start': '正在分析目标客群特征...',
            'complete': '客群分析完成'
        },
        'PLAN_CREATOR_AGENT': {
            'start': '正在制定营销方案...',
            'complete': '营销方案制定完成'
        },
        'COMPLETE_INTENT_NODE': {
            'start': '需要补充意图信息...',
            'complete': '意图信息补充完成'
        },
        'COMPLETE_CUSTOMER_NODE': {
            'start': '需要补充客群信息...',
            'complete': '客群信息补充完成'
        },
        'COMPLETE_PLAN_NODE': {
            'start': '需要完善营销方案...',
            'complete': '营销方案制定完成'
        }
    }
    
    @staticmethod
    def get_display_name(node_id: str) -> str:
        """获取节点的用户友好显示名称"""
        return NodeUtils.NODE_DISPLAY_NAMES.get(node_id, node_id)
    
    @staticmethod
    def get_node_index(node_id: str) -> int:
        """获取节点的执行顺序索引"""
        return NodeUtils.NODE_ORDER.get(node_id, 0)
    
    @staticmethod
    def generate_friendly_message(node_id: str, stage: str = 'start') -> str:
        """生成用户友好的进度消息"""
        node_messages = NodeUtils.FRIENDLY_MESSAGES.get(node_id, {})
        return node_messages.get(stage, f'{NodeUtils.get_display_name(node_id)}执行中...')


class ContentFilter:
    """内容过滤工具类"""
    
    # 技术关键词过滤列表
    TECH_KEYWORDS = {
        'json', 'nextNode', 'AGENT', 'intentType', 'confidence',
        'reasoning', 'threadId', 'messageId', 'conversationId'
    }
    
    @staticmethod
    def should_show_to_user(content: str) -> bool:
        """判断内容是否应该展示给用户"""
        if not content or len(content.strip()) < 3:
            return False
        
        content_lower = content.lower().strip()
        
        # 过滤技术关键词
        if any(keyword.lower() in content_lower for keyword in ContentFilter.TECH_KEYWORDS):
            return False
        
        # 过滤JSON结构
        if any(char in content for char in ['{', '}', '":', '","']):
            return False
        
        return True
    
    @staticmethod
    def filter_technical_content(content: str) -> str:
        """过滤技术内容，保留用户友好的部分"""
        if not ContentFilter.should_show_to_user(content):
            return ""
        return content.strip()


class ResponseFormatter:
    """响应格式化工具类"""
    
    @staticmethod
    def format_workflow_result(result: Dict[str, Any]) -> Dict[str, Any]:
        """统一的工作流结果格式化"""
        return {
            "success": result.get("success", True),
            "data": result.get("data", {}),
            "message": result.get("message", "操作完成"),
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def create_error_response(error: str, request: Any = None) -> Dict[str, Any]:
        """统一的错误响应创建"""
        return {
            "success": False,
            "data": {"error": error},
            "message": f"处理失败: {error}",
            "timestamp": datetime.now().isoformat(),
            "requestId": getattr(request, 'userId', None) if request else None
        }
    
    @staticmethod
    def create_success_response(data: Any = None, message: str = "操作成功") -> Dict[str, Any]:
        """统一的成功响应创建"""
        return {
            "success": True,
            "data": data or {},
            "message": message,
            "timestamp": datetime.now().isoformat()
        }


class ErrorHandler:
    """错误处理工具类"""
    
    @staticmethod
    def handle_error(error: Exception, context: str = "未知操作") -> Dict[str, Any]:
        """统一的错误处理"""
        error_message = str(error)
        logger.error(f"{context}失败: {error_message}")
        
        return {
            "success": False,
            "error": error_message,
            "context": context,
            "timestamp": datetime.now().isoformat()
        }
    
    @staticmethod
    def safe_execute(func, *args, **kwargs):
        """安全执行函数，自动处理异常"""
        try:
            return func(*args, **kwargs)
        except Exception as e:
            return ErrorHandler.handle_error(e, func.__name__)


class IDGenerator:
    """ID生成工具类"""
    
    @staticmethod
    def generate_message_id() -> str:
        """生成消息ID"""
        return f"msg_{str(uuid.uuid4())[:8]}_{int(datetime.now().timestamp() * 1000)}"
    
    @staticmethod
    def generate_conversation_id() -> str:
        """生成对话ID"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_thread_id(user_id: str, conversation_id: str) -> str:
        """生成线程ID"""
        return f"{user_id}_{conversation_id}"


def with_error_handling(operation_name: str = None):
    """统一的错误处理装饰器"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            op_name = operation_name or func.__name__
            try:
                logger.debug(f"开始执行: {op_name}")
                result = func(*args, **kwargs)
                logger.debug(f"执行完成: {op_name}")
                return result
            except Exception as e:
                logger.error(f"{op_name}执行失败: {str(e)}")
                return ErrorHandler.handle_error(e, op_name)
        return wrapper
    return decorator


def with_logging(func):
    """简单的日志装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger.info(f"开始执行: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.info(f"执行完成: {func.__name__}")
            return result
        except Exception as e:
            logger.error(f"执行失败: {func.__name__} - {str(e)}")
            raise
    return wrapper


# 便捷的全局函数，保持向后兼容
def getNodeDisplayName(nodeId: str) -> str:
    """向后兼容的节点显示名称获取函数"""
    return NodeUtils.get_display_name(nodeId)


def getNodeIndex(nodeId: str) -> int:
    """向后兼容的节点索引获取函数"""
    return NodeUtils.get_node_index(nodeId)


def shouldShowToUser(content: str) -> bool:
    """向后兼容的内容过滤函数"""
    return ContentFilter.should_show_to_user(content)


def generateFriendlyMessage(nodeId: str, stage: str = 'start') -> str:
    """向后兼容的友好消息生成函数"""
    return NodeUtils.generate_friendly_message(nodeId, stage)
