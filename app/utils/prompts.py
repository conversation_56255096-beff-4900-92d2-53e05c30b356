# prompts.py
"""
Prompt模板管理，每个Agent一个Prompt，支持动态变量插值
"""
from langchain.prompts import PromptTemplate

# 1. 意图识别Agent Prompt
intentExtractionPrompt = PromptTemplate(
    input_variables=["user_input", "history"],
    template="""
你是银行营销活动意图识别专家。请根据下方用户输入、历史上下文和参考案例，提取营销活动的关键信息，并判断是否有缺失。

【用户输入】
{user_input}

【历史上下文】
{history}

【参考历史案例】
- 活动目标：提升支付宝渠道绑卡率
- 目标客群：近90天未绑卡的受邀客户
- 活动时间：2024年4月1日-2024年12月31日
- 活动内容：通过支付宝绑定信用卡消费享受支付立减
- 激励方式：支付立减、话费、积分、实物礼品等
- 预算：10万元
- 触达渠道：短信+APP推送
- 参与限制：每人限一次，账户状态正常

请完成以下任务：
1. 提取以下字段（如无请留空）：
   - 核心要素（必须补全）：活动目标（goal）、预算（budget）、目标客群（audience）
   - 次要要素（可选）：活动名称（activity_name）、活动时间（period）、内容/规则（content）、激励方式（incentive）、触达渠道（channels）、参与限制（restriction）
2. 检查上述字段是否有缺失，并分别输出缺失的核心要素和次要要素列表。
3. 针对每个缺失字段，生成一句简洁的追问建议。
4. 针对每个字段，给出2-3条推荐建议（如常见目标、预算区间、客群类型等），如无可推荐内容则为空列表。

请严格按照如下JSON格式输出：
{
  "goal": "",
  "budget": "",
  "audience": "",
  "activity_name": "",
  "period": "",
  "content": "",
  "incentive": "",
  "channels": "",
  "restriction": "",
  "missing_core_fields": [],
  "missing_secondary_fields": [],
  "followup_questions": {
    "goal": "...",
    "budget": "...",
    "audience": "...",
    "activity_name": "...",
    ...
  },
  "field_suggestions": {
    "goal": ["提升绑卡率", "提升活跃率", "提升消费金额"],
    "budget": ["5万元", "10万元", "20万元"],
    "audience": ["新客户", "沉睡客户", "受邀客户"],
    "activity_name": ["支付宝绑卡立减活动", "信用卡消费抽奖活动"],
    "period": ["2024年7月-8月", "2024年Q3"],
    "content": ["消费满额抽奖", "绑卡送话费"],
    "incentive": ["话费", "积分", "实物礼品"],
    "channels": ["短信", "APP推送", "微信"],
    "restriction": ["每人限一次", "账户状态正常"]
  }
}
"""
)

# 2. 客群分析Agent Prompt
customerAnalysisPrompt = PromptTemplate(
    input_variables=["intentInfo"],
    template="""
你是一名客户标签分析专家，请根据以下营销活动意图信息，分析并输出详细的目标客群标签结构。

意图信息：{intentInfo}
请以JSON格式输出客群标签结构。
"""
)



# 4. 方案生成Agent Prompt
planGenerationPrompt = PromptTemplate(
    input_variables=["intentInfo", "ragContext"],
    template="""
你是一名资深营销策划师，请根据以下营销活动意图和历史案例经验，生成一份标准的营销活动方案，包含：目标、客群、渠道、激励、预算、预期效果等。

意图信息：{intentInfo}
历史案例经验：{ragContext}
请以Markdown格式输出完整方案。
"""
)

# 5. 方案评估Agent Prompt
planEvaluationPrompt = PromptTemplate(
    input_variables=["planDraft"],
    template="""
你是一名营销方案评审专家，请对以下营销活动方案进行评估，指出优缺点，并给出优化建议。

方案内容：{planDraft}
请以结构化文本输出评审意见。
"""
)

# 6. 对话管理Agent Prompt - 参考Manus的对话方式
conversationManagerPrompt = PromptTemplate(
    input_variables=["currentStage", "stateData", "conversationHistory"],
    template="""
你是一名智能营销对话管理专家，负责与用户进行有效沟通，确保营销方案生成过程的透明性和用户参与度。

当前阶段：{currentStage}
状态数据：{stateData}
对话历史：{conversationHistory}

请根据当前阶段生成适合的对话内容，包括：
1. 明确的阶段说明
2. 当前步骤的执行结果
3. 需要用户确认的关键信息
4. 可选的操作选项
5. 建议和指导

请以JSON格式输出对话内容。
"""
)

# 7. 反思Agent Prompt - 参考Manus的反思机制
reflectionPrompt = PromptTemplate(
    input_variables=["reflectionTarget", "stateData", "qualityCriteria"],
    template="""
你是一名智能营销反思专家，负责对营销方案生成过程中的各个环节进行质量评估和反思分析。

反思目标：{reflectionTarget}
状态数据：{stateData}
质量标准：{qualityCriteria}

请从以下维度进行反思分析：
1. **质量评估**：评估当前步骤的执行质量（0-1分）
2. **问题识别**：识别存在的问题和不足
3. **改进建议**：提出具体的优化建议
4. **重试必要性**：判断是否需要重新执行
5. **一致性检查**：检查与前置步骤的一致性

请以JSON格式输出反思结果，包含质量分数、问题列表、建议列表等。
"""
)

# 6. 渠道触达Agent Prompt
reachExecutionPrompt = PromptTemplate(
    input_variables=["planDraft"],
    template="""
你是一名营销渠道执行专家，请根据以下营销活动方案，生成各渠道（短信、外呼、智触等）的触达执行指令。

方案内容：{planDraft}
请以结构化文本输出各渠道执行要点。
"""
)

# 7. 效果分析Agent Prompt
effectAnalysisPrompt = PromptTemplate(
    input_variables=["reachResult"],
    template="""
你是一名营销效果分析师，请根据以下渠道触达结果，生成一份营销活动效果分析报告，包含ROI、转化率、用户反馈等。

触达结果：{reachResult}
请以结构化文本输出分析报告。
"""
)