# 智能营销系统 (BiFangBird)

基于 LangChain 和 LangGraph 的多Agent智能营销系统，实现从用户输入到营销方案生成的完整闭环。采用增强版路由Agent架构，集成记忆管理与会话跟踪功能，提供智能对话、自主反思、灵活循环的营销方案生成能力。

## 🚀 核心特性

### 🎯 当前核心功能
- **增强版路由Agent**: 基于LLM的智能路由决策，支持工具化架构和混合决策机制
- **多Agent协作**: 专业Agent协同工作，覆盖营销全流程
- **意图识别**: 自动提取用户营销意图和目标客群，支持多轮对话澄清
- **客群标签**: 智能生成客群标签和特征分析

- **方案生成**: 基于意图和历史案例生成完整营销方案
- **记忆管理**: 用户长期记忆、对话状态和会话缓存的分层管理
- **会话跟踪**: 基于用户ID、对话ID和会话ID的完整状态持久化

### 🤖 智能交互体验
- **自然语言对话**: 支持多轮对话和随时打断补充信息
- **智能路由决策**: LLM增强的路由选择，自动识别最佳执行路径
- **工具化架构**: 将分析逻辑封装为LangChain工具，支持LLM智能调用
- **混合决策机制**: LLM推理 + 规则验证的双重保障
- **中断恢复机制**: 支持工作流中断和恢复，实现人机交互
- **透明执行过程**: 实时展示路由决策和执行状态
- **完整记忆系统**: 用户长期记忆、对话历史和会话状态的智能管理

## 📁 项目结构

```
BiFangBird/
├── app/
│   ├── agents/                       # Agent组件
│   │   ├── base.py                      # 基础Agent类和装饰器
│   │   ├── RouterAgent.py               # 智能路由Agent（LLM增强）
│   │   ├── IntentionAgent.py            # 意图识别Agent
│   │   ├── TagCustomerAgent.py          # 客群标签Agent

│   │   ├── PlanCreatorAgent.py          # 方案生成Agent
│   │   └── __init__.py
│   ├── workflows/
│   │   ├── MarketingWorkflow.py         # 主工作流（基于LangGraph）
│   │   └── __init__.py
│   ├── core/
│   │   ├── config.py                    # 系统配置
│   │   ├── MemoryManager.py             # 内存管理器
│   │   ├── checkpointer.py              # 状态持久化
│   │   └── __init__.py
│   ├── llm/
│   │   ├── LlmFactory.py                # LLM工厂类
│   │   └── __init__.py
│   ├── utils/
│   │   ├── models.py                    # 核心数据模型
│   │   ├── routingModels.py             # 路由决策模型
│   │   └── __init__.py
│   ├── api/
│   │   ├── app.py                       # FastAPI应用
│   │   ├── routes.py                    # API路由
│   │   └── __init__.py
│   └── __init__.py
├── test/                             # 测试文件
│   ├── test_workflow_interactive.py     # 交互式工作流测试
│   ├── test_llm_connection.py           # LLM连接测试
│   └── ...
├── main.py                           # 主入口（API服务模式）
├── simple_test.py                    # 基础功能测试
├── test_routing_decisions.py         # 路由决策测试
└── README.md                         # 项目说明
```

### 🏗️ 核心架构说明

- **EnhancedRouterAgent**: 使用LLM增强的智能路由决策，支持工具化架构
- **MarketingWorkflow**: 基于LangGraph的状态机工作流，支持中断恢复
- **MemoryManager**: 分层内存管理，支持用户长期记忆和会话状态
- **routingModels**: 完整的路由决策数据模型，支持决策追踪和调试

## 🛠️ 安装和使用

### 1. 环境要求

- Python 3.9+
- 支持的LLM: OpenAI GPT、阿里云DashScope等
- 可选: Redis（用于长期记忆存储）

### 2. 安装依赖

```bash
# 克隆项目
git clone https://gitee.com/yaboliu/bi-fang-bird.git
cd BiFangBird

# 安装核心依赖
pip install langchain langchain-openai langgraph loguru

# 或安装完整依赖（如果有requirements.txt）
pip install -r requirements.txt
```

### 3. 配置环境

创建 `.env` 文件并配置：

```env
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key
OPENAI_BASE_URL=https://api.openai.com/v1

# 阿里云DashScope配置（可选）
DASHSCOPE_API_KEY=your_dashscope_api_key

# 系统配置
ENVIRONMENT=development
LOG_LEVEL=INFO

# Redis配置（可选，用于长期记忆）
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_password
```

### 4. 运行方式

#### API服务模式（主要方式）
```bash
# 启动FastAPI服务
python main.py

# 或者直接使用uvicorn
python -m uvicorn app.api.app:app --host 0.0.0.0 --port 8000

# 访问API文档
# http://localhost:8000/docs
```

#### 功能测试
```bash
# 运行基础功能测试
python simple_test.py

# 运行路由决策测试
python test_routing_decisions.py

# 运行工作流测试
python test/test_workflow_interactive.py
```

## 📋 使用示例

### 🎯 快速开始

**推荐方式：API服务模式**
```bash
# 启动API服务
python main.py

# 访问API文档
# http://localhost:8000/docs
```

### 🔧 编程接口使用

```python
from app.workflows.MarketingWorkflow import MarketingWorkflow
from app.utils.models import MarketingState

# 创建工作流实例
workflow = MarketingWorkflow()

# 构建初始状态
initial_state = MarketingState(
    userInput="我想做一个产品推广活动",
    messages=[],
    intentInfo={},
    customerTags={},
    planDraft={},
    currentStep="start",
    conversationHistory=[]
)

# 执行工作流
config = {"configurable": {"thread_id": "user_123"}}
result = workflow.app.invoke(initial_state, config=config)

print(f"路由决策: {result.get('routingDecision', {})}")
```

### 🌐 API调用示例

```python
import requests

# 智能营销对话接口
response = requests.post(
    "http://localhost:8000/api/v1/marketing/chat",
    json={
        "userId": "user_123",
        "userInput": "我想做一个产品推广活动",
        "conversationId": "conv_456"
    }
)

result = response.json()
print(f"系统回复: {result['data']['reply']}")
print(f"路由决策: {result['data']['routingDecision']}")
```

### 🧪 测试增强版路由Agent

```python
from app.agents.EnhancedRouterAgent import EnhancedRouterAgent
from app.utils.models import MarketingState

# 创建增强版路由Agent
router = EnhancedRouterAgent(enableLlmRouting=True)

# 测试路由决策
state = MarketingState(
    userInput="我想做营销活动",
    messages=[],
    intentInfo={},
    customerTags={},
    planDraft={},
    currentStep="start",
    conversationHistory=[]
)

result = router.execute(state)
routing_decision = result.get('routingDecision', {})

print(f"下一个Agent: {routing_decision.get('nextAgent')}")
print(f"意图类型: {routing_decision.get('intentType')}")
print(f"推理过程: {routing_decision.get('reasoning')}")
```

## 🔄 工作流程

### 增强版智能工作流

```mermaid
graph TD
    A[用户输入] --> B[EnhancedRouterAgent]
    B --> C{智能路由决策}
    C -->|意图不明确| D[IntentionAgent]
    C -->|需要补全意图| E[CompleteIntentNode]

    C -->|客群分析| G[TagCustomerAgent]
    C -->|生成方案| H[PlanCreatorAgent]

    D --> I[意图识别完成]
    I --> B

    E --> J[意图补全完成]
    J --> B



    G --> L[客群标签完成]
    L --> B

    H --> M[方案生成完成]
    M --> N[END]
```

### 增强版路由Agent决策流程

```mermaid
graph TD
    A[接收用户输入] --> B[LLM工具调用]
    B --> C[意图分析工具]
    B --> D[状态分析工具]
    B --> E[信息充分性评估工具]

    C --> F[意图识别结果]
    D --> G[状态分析结果]
    E --> H[充分性评估结果]

    F --> I[综合决策分析]
    G --> I
    H --> I

    I --> J[生成路由决策]
    J --> K[规则验证]
    K --> L{验证通过?}

    L -->|是| M[执行路由决策]
    L -->|否| N[兜底路由逻辑]

    M --> O[更新状态]
    N --> O
```

### 核心执行步骤

1. **RouterAgent**: 智能路由决策，基于LLM分析和规则验证
2. **IntentionAgent**: 深度意图识别和需求澄清
3. **TagCustomerAgent**: 客群标签生成和特征分析
4. **PlanCreatorAgent**: 营销方案生成和优化
5. **补全节点**: 处理信息不完整的情况，引导用户补充
6. **记忆管理**: 全程跟踪用户状态和对话历史

## 🎯 Agent详细说明

### 核心路由Agent

#### RouterAgent
- **功能**: 智能路由决策和流程控制
- **特性**:
  - LLM增强的智能分析和决策
  - 工具化架构，支持动态工具调用
  - 混合决策机制（LLM推理 + 规则验证）
  - 完整的兜底机制和错误处理
- **工具集**:
  - 意图分析工具：识别用户营销意图类型
  - 状态分析工具：评估当前工作流状态
  - 信息充分性评估工具：判断是否需要补充信息
  - 路由决策工具：生成最优的下一步执行路径

### 核心业务Agent

#### IntentionAgent
- **功能**: 营销意图识别和需求澄清
- **输入**: 用户自然语言描述
- **输出**: 结构化意图信息和补全建议
- **特性**:
  - 深度意图挖掘和分类
  - 智能缺失字段识别
  - 引导话术生成
  - 多轮对话支持

#### TagCustomerAgent
- **功能**: 目标客群分析和标签生成
- **输入**: 营销意图信息
- **输出**: 客群标签结构和特征描述
- **特性**: 多维度标签、精准圈选、业务规则验证



#### PlanCreatorAgent
- **功能**: 营销方案生成和优化
- **输入**: 意图、客群、历史案例
- **输出**: 完整的营销策划方案
- **特性**: 模板化生成、个性化定制、多方案对比

### 辅助功能组件

#### 补全节点 (Complete Nodes)
- **CompleteIntentNode**: 意图信息补全
- **CompleteCustomerNode**: 客群信息补全
- **CompletePlanNode**: 方案信息补全
- **特性**: 智能引导、用户友好的信息收集

#### 记忆管理系统
- **MemoryManager**: 分层内存管理
- **ConversationState**: 对话状态跟踪
- **特性**: 用户长期记忆、会话状态持久化、历史摘要

## 🔧 开发指南

### 添加新Agent

1. **继承基类**
```python
from app.agents.base import BaseAgent, LLMAgent

class MyCustomAgent(LLMAgent):
    def __init__(self, llm=None):
        super().__init__(llm)

    def validate_input(self, state) -> bool:
        return True

    def get_required_fields(self) -> List[str]:
        return ["requiredField"]

    def execute(self, state) -> AgentResult:
        # 实现具体逻辑
        pass
```

2. **注册到容器**
```python
# 在 app/core/container.py 中注册
container.register_singleton(
    "my_custom_agent",
    lambda llm, config: MyCustomAgent(llm),
    dependencies=["llm"]
)
```

3. **集成到工作流**
```python
# 在 MarketingWorkflow 中添加节点
workflow.add_node("my_custom", my_custom_agent.execute)
workflow.add_edge("previous_node", "my_custom")
```

### 自定义Prompt模板

```python
# 在 app/utils/prompts.py 中添加
MY_CUSTOM_PROMPT = PromptTemplate(
    input_variables=["input_var"],
    template="""
    你是一个专业的{role}，请根据以下信息：
    {input_var}

    生成相应的结果。
    """
)
```

### 扩展数据模型

```python
# 在 app/utils/models.py 中定义
from pydantic import BaseModel
from typing import Optional, List

class MyCustomModel(BaseModel):
    field1: str
    field2: Optional[int] = None
    field3: List[str] = []

    class Config:
        extra = "allow"
```

### 配置管理

```python
# 在 app/core/config.py 中添加配置
class SystemConfig(BaseSettings):
    my_custom_setting: str = "default_value"
    my_custom_enabled: bool = True

    class Config:
        env_file = ".env"
```

### 集成短期记忆prompt到自定义Agent

1. **在Agent的execute方法中获取ConversationState对象**
```python
conversationState = state.get("conversationState")
```
2. **组织本阶段需要的结构化数据**
```python
structuredData = {"意图": ..., "用户输入": ...}
```
3. **用buildPrompt构建prompt**
```python
prompt = conversationState.buildPrompt(structuredData, n=10)
```
4. **将prompt传递给LLM或用于日志分析**

> 这样即可让你的Agent自动具备短期记忆能力，提升多轮对话和复杂流程的上下文理解。

## 🚀 功能路线图

### ✅ 当前版本功能（已完成）
- [x] 增强版路由Agent架构
- [x] LLM增强的智能路由决策
- [x] 工具化架构和混合决策机制
- [x] 意图识别和需求澄清
- [x] 客群标签生成
- [x] 历史数据RAG检索
- [x] 营销方案生成
- [x] 分层记忆管理系统
- [x] 完整的API服务接口
- [x] 状态持久化和会话跟踪

### 🔄 下一阶段功能（规划中）
- [ ] 方案质量评估和优化
- [ ] 多渠道执行支持
- [ ] 效果分析和ROI计算
- [ ] 实时数据集成
- [ ] 高级个性化推荐
- [ ] 可视化界面和仪表板

### 📋 长期规划功能
- [ ] 多模态交互支持
- [ ] 自动化A/B测试
- [ ] 智能预算优化
- [ ] 跨平台渠道整合
- [ ] 实时监控和告警
- [ ] 机器学习模型优化

## 📊 性能指标

基于当前增强版路由Agent架构的性能表现：

- **路由决策成功率**: 100%（基于测试结果）
- **意图识别准确率**: > 80%（支持咨询类、方案生成类等多种意图）
- **API响应时间**: < 2秒（标准路由决策）
- **系统稳定性**: 完善的兜底机制，确保系统可靠运行

### 🧪 测试结果展示

最新的路由决策测试结果：

```
=== 测试结果汇总 ===
总测试用例: 5
成功执行: 5
成功率: 100.0%
🎉 所有路由决策测试通过！

测试用例包括：
✓ 方案生成需求 → 路由到 INTENTION_AGENT
✓ 咨询类需求 → 路由到 INTENTION_AGENT
✓ 模糊输入 → 路由到 INTENTION_AGENT
✓ 产品推广需求 → 路由到 INTENTION_AGENT
✓ 市场分析咨询 → 路由到 INTENTION_AGENT
```

## 🔍 监控和测试

系统提供完整的测试和监控功能：

```bash
# 基础功能测试
python simple_test.py

# 路由决策功能测试
python test_routing_decisions.py

# API健康检查
curl http://localhost:8000/api/v1/health

# 查看API文档
# http://localhost:8000/docs
```

## 📝 版本历史

- **v3.0.0**: 增强版路由Agent架构，完全移除原版路由Agent
- **v2.1.0**: 增加Manus式对话和反思机制
- **v2.0.0**: 重构为标准化多Agent架构
- **v1.0.0**: 初始版本

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

### 代码规范

- 使用驼峰命名法
- 添加类型注解
- 编写单元测试
- 更新文档

## 📞 联系方式

- 项目地址: [https://gitee.com/yaboliu/bi-fang-bird](https://gitee.com/yaboliu/bi-fang-bird)
- 问题反馈: 提交 Issue
- 技术交流: 欢迎 PR 和讨论

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件